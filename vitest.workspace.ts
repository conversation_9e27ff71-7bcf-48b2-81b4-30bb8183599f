import { defineWorkspace } from 'vitest/config'

export default defineWorkspace([
  {
    extends: './apps/web/vitest.config.ts',
    test: {
      name: 'web',
      root: './apps/web',
      environment: 'jsdom',
      coverage: {
        provider: 'v8',
        reporter: ['text', 'text-summary', 'lcov'],
        reportsDirectory: './coverage',
      },
    },
  },
  {
    test: {
      name: 'bff',
      root: './apps/bff',
      environment: 'node',
      coverage: {
        provider: 'v8',
        reporter: ['text', 'text-summary', 'lcov'],
        reportsDirectory: './coverage',
      },
    },
  },
  {
    test: {
      name: 'domain-bank',
      root: './packages/domain-bank',
      environment: 'node',
      coverage: {
        provider: 'v8',
        reporter: ['text', 'text-summary', 'lcov'],
        reportsDirectory: './coverage',
      },
    },
  },
  {
    test: {
      name: 'domain-ledger',
      root: './packages/domain-ledger',
      environment: 'node',
      coverage: {
        provider: 'v8',
        reporter: ['text', 'text-summary', 'lcov'],
        reportsDirectory: './coverage',
      },
    },
  },
  {
    test: {
      name: 'domain-vat',
      root: './packages/domain-vat',
      environment: 'node',
      coverage: {
        provider: 'v8',
        reporter: ['text', 'text-summary', 'lcov'],
        reportsDirectory: './coverage',
      },
    },
  },
  {
    test: {
      name: 'types',
      root: './packages/types',
      environment: 'node',
      coverage: {
        provider: 'v8',
        reporter: ['text', 'text-summary', 'lcov'],
        reportsDirectory: './coverage',
      },
    },
  },
])

