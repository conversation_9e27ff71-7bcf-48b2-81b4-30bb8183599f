#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Block commits of locally-generated supabase.ts
if [ -f "packages/types/src/supabase.ts" ]; then
  if grep -q "Generated from.*local" packages/types/src/supabase.ts 2>/dev/null; then
    echo "❌ Blocking commit: supabase.ts was generated locally"
    echo "   This file should only be updated by CI from staging environment"
    echo "   Run: git checkout packages/types/src/supabase.ts"
    echo "   Or: pnpm db:types:staging (if you're a maintainer)"
    exit 1
  fi
fi

# Run linting with auto-fix on staged files
npx lint-staged