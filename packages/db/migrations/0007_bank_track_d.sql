-- Track D: Bank statement flow enhancements (forward-only)
-- Adds dedupe/status to bank_transactions and a link table for reconciliations

-- Columns on bank_transactions for idempotent import and observability
ALTER TABLE bank_transactions
ADD COLUMN IF NOT EXISTS batch_id TEXT
, ADD COLUMN IF NOT EXISTS dedupe_hash TEXT
, ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'EUR'
, ADD COLUMN IF NOT EXISTS structured_ref TEXT
, ADD COLUMN IF NOT EXISTS raw_json JSONB
, ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'unmatched'
    CHECK (status IN ('unmatched', 'proposed', 'matched', 'reconciled', 'failed'));

-- Unique constraint for idempotent import at account scope
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'uq_bank_transactions_account_hash'
  ) THEN
    ALTER TABLE bank_transactions
      ADD CONSTRAINT uq_bank_transactions_account_hash
      UNIQUE (bank_account_id, dedupe_hash);
  END IF;
END $$;

-- Helpful index for listing/filtering by account/status/date
CREATE INDEX IF NOT EXISTS idx_bt_account_status_date
ON bank_transactions (bank_account_id, status, transaction_date DESC);

-- Link table: bank transaction -> AR/AP invoice or journal (supports partials)
CREATE TABLE IF NOT EXISTS bank_tx_links (
  id BIGSERIAL PRIMARY KEY
  , entity_id BIGINT NOT NULL REFERENCES entities (id) ON DELETE CASCADE
  , bank_transaction_id BIGINT NOT NULL
    REFERENCES bank_transactions (id) ON DELETE CASCADE
  , kind TEXT NOT NULL CHECK (kind IN ('AR', 'AP', 'JOURNAL'))
  , invoice_id BIGINT   -- when AR/AP
  , journal_id BIGINT   -- when matched to a journal directly
  , amount_applied NUMERIC(18, 2) NOT NULL
  , created_by UUID DEFAULT auth.uid()
  , created_at TIMESTAMPTZ DEFAULT NOW()
);

ALTER TABLE bank_tx_links ENABLE ROW LEVEL SECURITY;

-- RLS policies based on entity membership
CREATE POLICY IF NOT EXISTS bank_tx_links_select ON bank_tx_links
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM entity_memberships em
    WHERE em.entity_id = bank_tx_links.entity_id
      AND em.user_id = auth.uid()
  )
);

CREATE POLICY IF NOT EXISTS bank_tx_links_insert ON bank_tx_links
FOR INSERT TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM entity_memberships em
    WHERE em.entity_id = bank_tx_links.entity_id
      AND em.user_id = auth.uid()
      AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  )
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_btl_entity_tx
ON bank_tx_links (entity_id, bank_transaction_id);