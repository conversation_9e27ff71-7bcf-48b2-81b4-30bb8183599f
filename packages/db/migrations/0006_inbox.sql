-- Inbox E2E - Document Processing Migration
-- Creates tables for document upload, AI extraction, review and processing

-- Inbox documents table (entity-scoped, RLS enforced)
-- Note: Separate from existing 'documents' table which handles file storage references
CREATE TABLE IF NOT EXISTS inbox_documents (
  id BIGSERIAL PRIMARY KEY
  , entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE
  , path TEXT NOT NULL -- storage key e.g. 'inbox/{entity_id}/{uuid}.pdf'
  , file_hash TEXT NOT NULL -- sha256 of bytes for deduplication
  , mime_type TEXT NOT NULL
  , source TEXT NOT NULL CHECK (source IN ('upload', 'email', 'api'))
  , status TEXT NOT NULL CHECK (status IN (
      'uploaded'     -- File uploaded, not yet processed
      , 'extracted'    -- AI extraction completed
      , 'suggested'    -- Journal lines suggested
      , 'confirmed'    -- User confirmed, ready to post/export
      , 'posted'       -- Posted to journal (ledger mode)
      , 'exported'     -- Exported for integration (assist mode)
      , 'failed'       -- Processing failed
    ))
  , extraction JSONB           -- Raw AI extraction payload
  , confidence NUMERIC CHECK (confidence >= 0 AND confidence <= 1) -- 0..1 overall confidence score
  , suggestion JSONB           -- Normalized journal lines we propose to post
  , posted_journal_id BIGINT   -- Set in ledger mode after posting
  , export_ref TEXT            -- Export bundle id/path in assist mode
  , error_msg TEXT             -- Error message if processing failed
  , created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
  , updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Unique constraint on entity + file hash for deduplication
CREATE UNIQUE INDEX IF NOT EXISTS uq_inbox_documents_entity_filehash
  ON inbox_documents(entity_id, file_hash);

-- Index for efficient querying by entity and status
CREATE INDEX IF NOT EXISTS idx_inbox_documents_entity_status_created
  ON inbox_documents(entity_id, status, created_at DESC);

-- Index for posted journals lookup
CREATE INDEX IF NOT EXISTS idx_inbox_documents_posted_journal
  ON inbox_documents(posted_journal_id) WHERE posted_journal_id IS NOT NULL;

-- Enable RLS on inbox_documents table
ALTER TABLE inbox_documents ENABLE ROW LEVEL SECURITY;

-- RLS policy for reading inbox documents (entity membership required)
CREATE POLICY IF NOT EXISTS inbox_documents_select ON inbox_documents
  FOR SELECT USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
    )
  );

-- RLS policy for inserting inbox documents (entity membership with write permissions)
CREATE POLICY IF NOT EXISTS inbox_documents_insert ON inbox_documents
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

-- RLS policy for updating inbox documents (with proper USING and WITH CHECK)
CREATE POLICY IF NOT EXISTS inbox_documents_update ON inbox_documents
  FOR UPDATE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  ) WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

-- RLS policy for deleting inbox documents (admin/owner only)
CREATE POLICY IF NOT EXISTS inbox_documents_delete ON inbox_documents
  FOR DELETE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  );

-- Supplier templates for auto-suggestions (optional, for later improvements)
CREATE TABLE IF NOT EXISTS supplier_templates (
  id BIGSERIAL PRIMARY KEY
  , entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE
  , supplier_vat TEXT  -- Normalized supplier VAT number
  , supplier_name TEXT -- For matching when VAT not available
  , default_account_id BIGINT REFERENCES accounts(id)
  , default_vat_code_id BIGINT REFERENCES vat_codes(id)
  , last_used_at TIMESTAMPTZ
  , created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
  , updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
  , UNIQUE (entity_id, supplier_vat)
);

-- Enable RLS on supplier_templates
ALTER TABLE supplier_templates ENABLE ROW LEVEL SECURITY;

-- RLS policies for supplier_templates (same as documents)
CREATE POLICY supplier_templates_select ON supplier_templates
  FOR SELECT USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = supplier_templates.entity_id
        AND em.user_id = auth.uid()
    )
  );

CREATE POLICY IF NOT EXISTS supplier_templates_insert ON supplier_templates
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = supplier_templates.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

CREATE POLICY IF NOT EXISTS supplier_templates_update ON supplier_templates
  FOR UPDATE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = supplier_templates.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  ) WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = supplier_templates.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

CREATE POLICY IF NOT EXISTS supplier_templates_delete ON supplier_templates
  FOR DELETE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = supplier_templates.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

-- Feature flags table for dark deployment control
CREATE TABLE IF NOT EXISTS feature_flags (
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE
  , flag TEXT NOT NULL
  , enabled BOOLEAN NOT NULL DEFAULT false
  , config JSONB DEFAULT '{}'
  , created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
  , updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
  , PRIMARY KEY (entity_id, flag)
);

-- Enable RLS on feature_flags
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;

-- RLS policy for feature flags (entity membership required to read)
CREATE POLICY feature_flags_select ON feature_flags
  FOR SELECT USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = feature_flags.entity_id
        AND em.user_id = auth.uid()
    )
  );

-- Only owners/admins can manage feature flags
CREATE POLICY IF NOT EXISTS feature_flags_insert ON feature_flags
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = feature_flags.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  );

CREATE POLICY IF NOT EXISTS feature_flags_update ON feature_flags
  FOR UPDATE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = feature_flags.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  ) WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = feature_flags.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  );

CREATE POLICY IF NOT EXISTS feature_flags_delete ON feature_flags
  FOR DELETE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = feature_flags.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  );

-- Add updated_at trigger for inbox_documents
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER IF NOT EXISTS inbox_documents_updated_at
  BEFORE UPDATE ON inbox_documents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS supplier_templates_updated_at
  BEFORE UPDATE ON supplier_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS feature_flags_updated_at
  BEFORE UPDATE ON feature_flags
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE inbox_documents IS 'Document processing pipeline: upload -> extract -> suggest -> confirm -> post/export';
COMMENT ON COLUMN inbox_documents.extraction IS 'Raw AI extraction result in JSON format following ExtractionResult schema';
COMMENT ON COLUMN inbox_documents.suggestion IS 'Balanced journal lines ready for posting, following Suggestion schema';
COMMENT ON COLUMN inbox_documents.file_hash IS 'SHA256 hash of file content for deduplication';
COMMENT ON COLUMN inbox_documents.confidence IS 'AI extraction confidence score 0-1, helps flag manual review needs';

COMMENT ON TABLE supplier_templates IS 'Learned supplier preferences for account/VAT code suggestions';
COMMENT ON TABLE feature_flags IS 'Per-entity feature toggles for dark deployment and controlled rollout';