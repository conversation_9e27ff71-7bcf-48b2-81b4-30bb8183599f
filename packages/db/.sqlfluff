[sqlfluff]
dialect = postgres
templater = jinja
sql_file_exts = .sql,.psql
max_line_length = 120

[sqlfluff:indentation]
indented_joins = False
indented_ctes = False
indented_using_on = True
template_blocks_indent = True
tab_space_size = 2
indent_unit = space

[sqlfluff:layout:type:comma]
line_position = leading

[sqlfluff:rules]
aliasing = explicit

[sqlfluff:rules:capitalisation.keywords]
capitalisation_policy = upper

[sqlfluff:rules:capitalisation.identifiers]
unquoted_identifiers_policy = all

[sqlfluff:rules:capitalisation.functions]
capitalisation_policy = upper