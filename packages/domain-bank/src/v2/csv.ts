export type CsvMapping = {
  bookingDate: string
  valueDate?: string
  amount: string
  reference?: string
  counterparty?: string
}

export type CsvConfig = {
  delimiter?: string
  headers?: boolean
  map: CsvMapping
}

export function parseCsv(csvText: string, config: CsvConfig) {
  const delimiter = config.delimiter ?? ','
  const rows = csvText.split(/\r?\n/).filter(r => r.trim().length > 0)
  if (rows.length === 0) return { entries: [] as any[] }

  let header: string[] | null = null
  const entries: any[] = []

  if (config.headers !== false) {
    header = splitCsvLine(rows[0], delimiter)
  }

  for (let i = header ? 1 : 0; i < rows.length; i++) {
    const cols = splitCsvLine(rows[i], delimiter)
    const get = (key?: string) => {
      if (!key) return undefined
      if (header) {
        const idx = header.findIndex(h => h.trim().toLowerCase() === key.trim().toLowerCase())
        return idx >= 0 ? cols[idx] : undefined
      }
      const idx = parseInt(key, 10)
      return Number.isFinite(idx) ? cols[idx] : undefined
    }

    const bookingDate = normalizeDate(get(config.map.bookingDate) || '')
    const valueDate = normalizeDate(get(config.map.valueDate || config.map.bookingDate) || '')
    const amountStr = (get(config.map.amount) || '').toString()
    const amount = normalizeAmount(amountStr)
    const reference = get(config.map.reference || '')
    const counterparty = get(config.map.counterparty || '')

    if (!bookingDate || amount === undefined) continue

    entries.push({
      bookingDate,
      valueDate,
      amount: amount.toFixed(2),
      reference: reference || undefined,
      counterparty: counterparty || undefined,
      raw: { row: rows[i] }
    })
  }

  return { entries }
}

function splitCsvLine(line: string, delimiter: string): string[] {
  const values: string[] = []
  let cur = ''
  let inQ = false
  for (let i = 0; i < line.length; i++) {
    const ch = line[i]
    if (ch === '"') inQ = !inQ
    else if (ch === delimiter && !inQ) { values.push(cur); cur = '' } else cur += ch
  }
  values.push(cur)
  return values.map(v => v.replace(/^\"|\"$/g, '').trim())
}

function normalizeDate(s: string): string | undefined {
  const m1 = s.match(/^(\d{4})-(\d{2})-(\d{2})$/)
  if (m1) return s
  const m2 = s.match(/^(\d{2})[\/\-.](\d{2})[\/\-.](\d{4})$/)
  if (m2) return `${m2[3]}-${m2[2]}-${m2[1]}`
  return undefined
}

function normalizeAmount(s: string): number | undefined {
  const cleaned = s.replace(/[^0-9,.-]/g, '').replace(',', '.')
  const n = Number(cleaned)
  return Number.isFinite(n) ? n : undefined
}

