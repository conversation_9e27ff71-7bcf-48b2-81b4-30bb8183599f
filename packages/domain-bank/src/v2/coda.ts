export type ParsedBatch = {
  accountIban?: string
  statementDate?: string
  currency?: string
  entries: Array<{
    bookingDate: string
    valueDate?: string
    amount: string
    reference?: string
    structuredRef?: string
    counterparty?: string
    sequence?: string
    raw: Record<string, any>
  }>
}

const STRUCTURED_REF_REGEX = /\+\+\+\s*([0-9]{3})\/?([0-9]{4})\/?([0-9]{5})\s*\+\+\+/;

function normDateYYMMDD(s: string): string | undefined {
  // Accept 6-digit YYMMDD and return YYYY-MM-DD (assume 20xx for YY<70 else 19xx)
  const m = s.match(/^(\d{2})(\d{2})(\d{2})$/)
  if (!m) return undefined
  const yy = parseInt(m[1], 10)
  const yyyy = yy < 70 ? 2000 + yy : 1900 + yy
  return `${yyyy}-${m[2]}-${m[3]}`
}

function sanitizeAmount(raw: string): string | undefined {
  // Accept amounts with sign and decimal separators
  const cleaned = raw.replace(/[^0-9,.-]/g, '').replace(',', '.')
  if (!cleaned) return undefined
  const n = Number(cleaned)
  if (Number.isNaN(n)) return undefined
  return n.toFixed(2)
}

export function parseCoda(input: ArrayBuffer | string): ParsedBatch {
  const text = typeof input === 'string' ? input : new TextDecoder().decode(new Uint8Array(input))
  const lines = text.split(/\r?\n/).filter(l => l.trim().length > 0)

  const batch: ParsedBatch = { entries: [] }

  // Minimal/tolerant CODA v2 parsing:
  // - Header lines may start with 0000 / 0
  // - Transaction lines often start with 1 (main) and 2 (structured info)
  // We extract best-effort date/amount/reference from obvious tokens.

  let seqCounter = 0
  for (const line of lines) {
    const raw: Record<string, any> = { line }
    // Try to locate structured ref anywhere in the line
    const structuredMatch = line.match(STRUCTURED_REF_REGEX)
    const structuredRef = structuredMatch ? `+++${structuredMatch[1]}/${structuredMatch[2]}/${structuredMatch[3]}+++` : undefined

    // Heuristics for amount/date
    // Find a 6-digit date token likely YYMMDD
    const dateToken = (line.match(/\b(\d{6})\b/) || [])[1]
    const bookingDate = normDateYYMMDD(dateToken || '')

    // Find a plausible amount token containing sign and decimals
    const amountToken = (line.match(/[+-]\s*\d+[,.]?\d{0,2}/) || [])[0] || (line.match(/\b\d+[,.]\d{2}\b/) || [])[0]
    const amount = sanitizeAmount(amountToken || '')

    // Try IBAN in header or any line
    const iban = (line.match(/[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7,}/) || [])[0]
    if (iban && !batch.accountIban) batch.accountIban = iban

    // Currency heuristic
    if (!batch.currency && /\b(EUR|USD|GBP)\b/.test(line)) {
      const cur = (line.match(/\b(EUR|USD|GBP)\b/) || [])[0]
      if (cur) batch.currency = cur
    }

    // Statement date heuristic: first parseable date we see becomes statementDate
    if (!batch.statementDate && bookingDate) batch.statementDate = bookingDate

    // Build entry only when we have the bare minimum
    if (bookingDate && amount) {
      seqCounter += 1
      batch.entries.push({
        bookingDate,
        amount,
        valueDate: bookingDate,
        reference: line,
        structuredRef,
        counterparty: undefined,
        sequence: String(seqCounter),
        raw
      })
    }
  }

  return batch
}

