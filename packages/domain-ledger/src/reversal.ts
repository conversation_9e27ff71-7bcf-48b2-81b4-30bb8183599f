import { PostJournal, JournalLine } from '@ledgerly/types'
import type { Database } from '@ledgerly/types/src/supabase'

type DatabaseJournalLine = Database['public']['Tables']['journal_lines']['Row']
type DatabaseJournal = Database['public']['Tables']['journals']['Row'] & {
  journal_lines: DatabaseJournalLine[]
}

export interface ReversalOptions {
  newTransactionDate?: string
  newReference?: string
  newDescription?: string
  reverseIndividualLines?: boolean
}

export class ReversalService {
  static generateReversalJournal(
    originalJournal: DatabaseJournal,
    options: ReversalOptions = {}
  ): PostJournal {
    const reversalDate = options.newTransactionDate || new Date().toISOString().split('T')[0]
    const reversalRef = options.newReference || `REV-${originalJournal.reference || originalJournal.id}`
    const reversalDesc = options.newDescription || `Reversal of: ${originalJournal.description}`

    const reversedLines: JournalLine[] = originalJournal.journal_lines.map((line: DatabaseJournalLine) => {
      const reversedLine: JournalLine = {
        journal_id: 0, // Will be set by database
        account_id: line.account_id,
        description: options.reverseIndividualLines 
          ? `Reversal: ${line.description || originalJournal.description}`
          : line.description || originalJournal.description
      }

      // Flip debit/credit amounts
      if (line.debit_amount) {
        reversedLine.credit_amount = line.debit_amount
      } else if (line.credit_amount) {
        reversedLine.debit_amount = line.credit_amount
      }

      // Copy VAT code if present
      if (line.vat_code_id) {
        reversedLine.vat_code_id = line.vat_code_id
      }

      return reversedLine
    })

    return {
      entity_id: originalJournal.entity_id,
      journal_type: `${originalJournal.journal_type}_reversal`,
      reference: reversalRef,
      description: reversalDesc,
      transaction_date: reversalDate,
      lines: reversedLines
    }
  }

  static createCorrectingEntry(
    originalJournal: DatabaseJournal,
    correctedLines: JournalLine[],
    correctionDate: string,
    correctionReason: string
  ): PostJournal[] {
    const journals: PostJournal[] = []

    // First, create reversal of original
    const reversalJournal = this.generateReversalJournal(originalJournal, {
      newTransactionDate: correctionDate,
      newReference: `CORR-REV-${originalJournal.reference || originalJournal.id}`,
      newDescription: `Correction reversal: ${correctionReason}`
    })

    journals.push(reversalJournal)

    // Then, create corrected journal
    const correctedJournal: PostJournal = {
      entity_id: originalJournal.entity_id,
      journal_type: `${originalJournal.journal_type}_corrected`,
      reference: `CORR-${originalJournal.reference || originalJournal.id}`,
      description: `Corrected: ${originalJournal.description} - ${correctionReason}`,
      transaction_date: correctionDate,
      lines: correctedLines
    }

    journals.push(correctedJournal)

    return journals
  }

  static createAdjustingEntry(
    entityId: number,
    description: string,
    transactionDate: string,
    adjustments: Array<{
      accountId: number
      currentAmount: number
      targetAmount: number
      isDebitAccount: boolean
    }>,
    reference?: string
  ): PostJournal | null {
    const adjustmentLines: JournalLine[] = []

    adjustments.forEach(adj => {
      const difference = adj.targetAmount - adj.currentAmount

      if (Math.abs(difference) < 0.01) {
        return // No adjustment needed
      }

      const line: JournalLine = {
        journal_id: 0,
        account_id: adj.accountId,
        description: `Adjustment: ${description}`
      }

      // For debit balance accounts (assets, expenses):
      // - Increase = debit, Decrease = credit
      // For credit balance accounts (liabilities, equity, revenue):
      // - Increase = credit, Decrease = debit
      if (adj.isDebitAccount) {
        if (difference > 0) {
          line.debit_amount = Math.abs(difference)
        } else {
          line.credit_amount = Math.abs(difference)
        }
      } else {
        if (difference > 0) {
          line.credit_amount = Math.abs(difference)
        } else {
          line.debit_amount = Math.abs(difference)
        }
      }

      adjustmentLines.push(line)
    })

    if (adjustmentLines.length === 0) {
      return null // No adjustments needed
    }

    // Ensure the adjustment journal balances
    const totalDebits = adjustmentLines.reduce((sum, line) => sum + (line.debit_amount || 0), 0)
    const totalCredits = adjustmentLines.reduce((sum, line) => sum + (line.credit_amount || 0), 0)
    const imbalance = totalDebits - totalCredits

    if (Math.abs(imbalance) > 0.01) {
      throw new Error(`Adjusting entry is not balanced. Imbalance: ${imbalance}`)
    }

    return {
      entity_id: entityId,
      journal_type: 'adjusting',
      reference: reference || `ADJ-${Date.now()}`,
      description: `Adjusting Entry: ${description}`,
      transaction_date: transactionDate,
      lines: adjustmentLines
    }
  }
}