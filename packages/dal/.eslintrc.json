{"root": false, "extends": ["../.eslintrc.json"], "parserOptions": {"project": "./tsconfig.json"}, "rules": {"@typescript-eslint/no-unsafe-assignment": "warn", "@typescript-eslint/no-unsafe-call": "warn", "@typescript-eslint/no-unsafe-member-access": "warn", "@typescript-eslint/no-unsafe-argument": "warn", "@typescript-eslint/no-unsafe-return": "warn", "@typescript-eslint/no-unnecessary-type-assertion": "warn", "@typescript-eslint/require-await": "warn"}}