import { SupabaseClient } from '@supabase/supabase-js'
import {
  PostJournal,
  PostJournalSchema,
  SwitchMode,
  SwitchModeSchema,
  GrantEntityRole,
  GrantEntityRoleSchema
} from '@ledgerly/types'

export class SupabaseRPC {
  constructor(private client: SupabaseClient<any>) {}

  async postJournal(input: PostJournal): Promise<{ journalId: number }> {
    // Validate input
    const validatedInput = PostJournalSchema.parse(input)
    
    // Call SQL RPC function
    const { data, error } = await (this.client.rpc as any)('rpc_post_journal', {
      p_entity: validatedInput.entity_id,
      p_type: validatedInput.journal_type,
      p_reference: validatedInput.reference || null,
      p_description: validatedInput.description,
      p_date: validatedInput.transaction_date,
      p_lines: validatedInput.lines
    })
    
    if (error) {
      throw new Error(`Failed to post journal: ${error.message}`)
    }
    
    if (!data || typeof data !== 'number') {
      throw new Error('Invalid response from rpc_post_journal')
    }
    
    return { journalId: data }
  }

  async switchOperatingMode(input: SwitchMode): Promise<{ success: boolean }> {
    // Validate input
    const validatedInput = SwitchModeSchema.parse(input)
    
    // Call SQL RPC function
    const { error } = await (this.client.rpc as any)('rpc_switch_mode', {
      p_entity: validatedInput.entity_id,
      p_mode: validatedInput.mode,
      p_config: validatedInput.config as any
    })
    
    if (error) {
      throw new Error(`Failed to switch mode: ${error.message}`)
    }
    
    return { success: true }
  }

  async grantEntityRole(input: GrantEntityRole): Promise<{ success: boolean }> {
    // Validate input
    const validatedInput = GrantEntityRoleSchema.parse(input)
    
    // Call SQL RPC function
    const { error } = await this.client.rpc('rpc_grant_entity_role', {
      p_entity: validatedInput.entity_id,
      p_user: validatedInput.user_id,
      p_role: validatedInput.role
    })
    
    if (error) {
      throw new Error(`Failed to grant role: ${error.message}`)
    }
    
    return { success: true }
  }

  async getEntityAccounts(entityId: number): Promise<any[]> {
    const { data, error } = await this.client
      .from('accounts')
      .select('id, code, name, account_type, normal_balance, is_active')
      .eq('entity_id', entityId)
      .eq('is_active', true)
      .order('code')
    
    if (error) {
      throw new Error(`Failed to fetch accounts: ${error.message}`)
    }
    
    return data || []
  }

  async getEntityJournals(entityId: number, limit = 50, offset = 0): Promise<any[]> {
    const { data, error } = await this.client
      .from('journals')
      .select(`
        id,
        journal_type,
        reference,
        description,
        transaction_date,
        created_at,
        is_balanced,
        journal_lines (
          id,
          account_id,
          description,
          debit_amount,
          credit_amount,
          accounts (
            code,
            name
          )
        )
      `)
      .eq('entity_id', entityId)
      .order('transaction_date', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)
    
    if (error) {
      throw new Error(`Failed to fetch journals: ${error.message}`)
    }
    
    return data || []
  }

  async getTrialBalance(_entityId: number, _asOfDate?: string): Promise<any[]> {
    // Simplified implementation to avoid TypeScript issues
    // TODO: Re-implement full trial balance calculation
    return []
  }

  /*
  async getTrialBalanceOld(entityId: number, asOfDate?: string): Promise<any[]> {
    let query = (this.client as any)
      .from('journal_lines')
      .select(`
        account_id,
        accounts!inner (
          code,
          name,
          account_type,
          normal_balance
        ),
        debit_amount,
        credit_amount,
        journals!inner (
          entity_id,
          transaction_date
        )
      `)
      .eq('journals.entity_id', entityId)
    
    if (asOfDate) {
      query = query.lte('journals.transaction_date', asOfDate)
    }
    
    const { data, error } = await query
    
    if (error) {
      throw new Error(`Failed to fetch trial balance: ${error.message}`)
    }
    
    // Calculate balances by account
    const balances: Record<number, any> = {}

    (data as any)?.forEach((line: any) => {
      const accountId = line.account_id
      const account = line.accounts

      if (!balances[accountId]) {
        balances[accountId] = {
          account_id: accountId,
          code: (account as any).code,
          name: (account as any).name,
          account_type: (account as any).account_type,
          normal_balance: (account as any).normal_balance,
          debit_total: 0,
          credit_total: 0,
          balance: 0
        }
      }

      const balance: any = balances[accountId]
      balance.debit_total += Number(line.debit_amount || 0)
      balance.credit_total += Number(line.credit_amount || 0)
    })
    
    // Calculate final balances based on account type
    const result = Object.values(balances).map((balance: any) => {
      const netBalance = balance.debit_total - balance.credit_total
      
      // For normal debit accounts (assets, expenses), positive means debit balance
      // For normal credit accounts (liabilities, equity, revenue), negative means credit balance
      if (balance.normal_balance === 'debit') {
        balance.balance = netBalance
      } else {
        balance.balance = -netBalance
      }
      
      return balance
    })
    
    return result.filter(balance => Math.abs(balance.balance) > 0.01)
  }
  */
}