/**
 * Security Events Data Access Layer
 * Functions for storing and querying security events
 */

import { createServerClient } from './client'
import type { SupabaseClientTyped } from './client'

export type SecurityEventType = 
  | 'AUTH_FAILED_LOGIN'
  | 'AUTH_SUCCESSFUL_LOGIN'
  | 'AUTH_PASSWORD_RESET'
  | 'RATE_LIMIT_EXCEEDED'
  | 'CSRF_VIOLATION'
  | 'SUSPICIOUS_REQUEST'
  | 'PERMISSION_DENIED'
  | 'DATA_EXPORT'
  | 'ADMIN_ACTION'

export interface SecurityEventInput {
  event_type: SecurityEventType
  user_id?: string
  session_id?: string
  ip_address?: string
  user_agent?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  resource?: string
  action?: string
  details?: Record<string, any>
}

export interface SecurityEvent extends SecurityEventInput {
  id: number
  created_at: string
}

export interface SecurityEventStats {
  event_type: string
  event_count: number
  unique_ips: number
  unique_users: number
  severity_breakdown: Record<string, number>
}

export interface SuspiciousIP {
  ip_address: string
  failed_logins: number
  rate_limit_violations: number
  total_events: number
  risk_score: number
}

/**
 * Store a security event in the database
 */
export async function storeSecurityEvent(
  event: SecurityEventInput,
  client?: SupabaseClientTyped
): Promise<SecurityEvent> {
  const supabase = client || createServerClient()
  
  const { data, error } = await (supabase as any)
    .from('security_events')
    .insert([{
      event_type: event.event_type,
      user_id: event.user_id || null,
      session_id: event.session_id || null,
      ip_address: event.ip_address || null,
      user_agent: event.user_agent || null,
      severity: event.severity,
      resource: event.resource || null,
      action: event.action || null,
      details: event.details || {}
    }])
    .select()
    .single()

  if (error) {
    console.error('Failed to store security event:', error)
    throw error
  }

  return data as SecurityEvent
}

/**
 * Get security events with filtering and pagination
 */
export async function getSecurityEvents(options: {
  event_types?: SecurityEventType[]
  user_id?: string
  ip_address?: string
  severity?: SecurityEvent['severity'][]
  time_window_hours?: number
  limit?: number
  offset?: number
} = {}, client?: SupabaseClientTyped): Promise<SecurityEvent[]> {
  const supabase = client || createServerClient()
  
  let query = (supabase as any)
    .from('security_events')
    .select('*')
    .order('created_at', { ascending: false })

  if (options.event_types && options.event_types.length > 0) {
    query = query.in('event_type', options.event_types)
  }

  if (options.user_id) {
    query = query.eq('user_id', options.user_id)
  }

  if (options.ip_address) {
    query = query.eq('ip_address', options.ip_address)
  }

  if (options.severity && options.severity.length > 0) {
    query = query.in('severity', options.severity)
  }

  if (options.time_window_hours) {
    const cutoff = new Date(Date.now() - options.time_window_hours * 60 * 60 * 1000).toISOString()
    query = query.gte('created_at', cutoff)
  }

  if (options.limit) {
    query = query.limit(options.limit)
  }

  if (options.offset) {
    query = query.range(options.offset, options.offset + (options.limit || 100) - 1)
  }

  const { data, error } = await query

  if (error) {
    console.error('Failed to get security events:', error)
    throw error
  }

  return (data as any) || []
}

/**
 * Get security event statistics
 */
export async function getSecurityEventStats(
  time_window_hours: number = 24,
  event_types?: SecurityEventType[],
  client?: SupabaseClientTyped
): Promise<SecurityEventStats[]> {
  const supabase = client || createServerClient()
  
  const { data, error } = await (supabase as any)
    .rpc('get_security_event_stats', {
      time_window_hours,
      event_types: event_types || null
    })

  if (error) {
    console.error('Failed to get security event stats:', error)
    throw error
  }

  return data as SecurityEventStats[]
}

/**
 * Detect suspicious IP addresses
 */
export async function detectSuspiciousIPs(
  time_window_minutes: number = 5,
  failed_login_threshold: number = 10,
  rate_limit_threshold: number = 20,
  client?: SupabaseClientTyped
): Promise<SuspiciousIP[]> {
  const supabase = client || createServerClient()
  
  const { data, error } = await (supabase as any)
    .rpc('detect_suspicious_ips', {
      time_window_minutes,
      failed_login_threshold,
      rate_limit_threshold
    })

  if (error) {
    console.error('Failed to detect suspicious IPs:', error)
    throw error
  }

  return data as SuspiciousIP[]
}

/**
 * Clean up old security events (for maintenance)
 */
export async function cleanupOldSecurityEvents(
  retention_days: number = 90,
  client?: SupabaseClientTyped
): Promise<number> {
  const supabase = client || createServerClient()
  
  const { data, error } = await (supabase as any)
    .rpc('cleanup_old_security_events', { retention_days })

  if (error) {
    console.error('Failed to cleanup old security events:', error)
    throw error
  }

  return data as number
}

/**
 * Count failed login attempts for an IP in a time window
 */
export async function getFailedLoginsByIP(
  ip_address: string,
  time_window_minutes: number = 5,
  client?: SupabaseClientTyped
): Promise<number> {
  const supabase = client || createServerClient()
  
  const cutoff = new Date(Date.now() - time_window_minutes * 60 * 1000).toISOString()
  
  const { count, error } = await (supabase as any)
    .from('security_events')
    .select('*', { count: 'exact', head: true })
    .eq('event_type', 'AUTH_FAILED_LOGIN')
    .eq('ip_address', ip_address)
    .gte('created_at', cutoff)

  if (error) {
    console.error('Failed to count failed logins:', error)
    return 0
  }

  return count || 0
}

/**
 * Count rate limit violations for an IP in a time window
 */
export async function getRateLimitViolationsByIP(
  ip_address: string,
  time_window_minutes: number = 5,
  client?: SupabaseClientTyped
): Promise<number> {
  const supabase = client || createServerClient()
  
  const cutoff = new Date(Date.now() - time_window_minutes * 60 * 1000).toISOString()
  
  const { count, error } = await (supabase as any)
    .from('security_events')
    .select('*', { count: 'exact', head: true })
    .eq('event_type', 'RATE_LIMIT_EXCEEDED')
    .eq('ip_address', ip_address)
    .gte('created_at', cutoff)

  if (error) {
    console.error('Failed to count rate limit violations:', error)
    return 0
  }

  return count || 0
}

/**
 * Check if an IP should be blocked based on recent activity
 */
export async function shouldBlockIP(
  ip_address: string,
  client?: SupabaseClientTyped
): Promise<boolean> {
  const [failedLogins, rateLimitViolations] = await Promise.all([
    getFailedLoginsByIP(ip_address, 5, client),
    getRateLimitViolationsByIP(ip_address, 5, client)
  ])
  
  return failedLogins >= 10 || rateLimitViolations >= 20
}