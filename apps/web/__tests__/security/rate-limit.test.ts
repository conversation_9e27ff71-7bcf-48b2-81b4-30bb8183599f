import {
  checkRateLimit,
  getClientIdentifier,
  createRateLimitHeaders,
  logRateLimitViolation,
  authRateLimiter
} from '@/lib/rate-limit'
import { NextRequest } from 'next/server'

describe('Rate Limiting', () => {
  let mockRequest: NextRequest

  beforeEach(() => {
    jest.clearAllMocks()
    mockRequest = new NextRequest('https://example.com', {
      headers: {
        'x-forwarded-for': '***********',
        'user-agent': 'Mozilla/5.0 Test'
      }
    })
  })

  describe('getClientIdentifier', () => {
    it('should return client IP from x-forwarded-for header', () => {
      const ip = getClientIdentifier(mockRequest)
      expect(ip).toBe('***********')
    })

    it('should handle missing headers gracefully', () => {
      const requestWithoutHeaders = new NextRequest('https://example.com')
      const ip = getClientIdentifier(requestWithoutHeaders)
      expect(ip).toBe('unknown')
    })

    it('should use x-real-ip when x-forwarded-for is not available', () => {
      const requestWithRealIP = new NextRequest('https://example.com', {
        headers: {
          'x-real-ip': '********'
        }
      })

      const ip = getClientIdentifier(requestWithRealIP)
      expect(ip).toBe('********')
    })
  })

  describe('checkRateLimit', () => {
    it('should allow requests within rate limit', () => {
      const result = checkRateLimit(mockRequest)

      expect(result.allowed).toBe(true)
      expect(result.remaining).toBeGreaterThanOrEqual(0)
      expect(result.resetTime).toBeGreaterThan(Date.now())
      expect(result.totalHits).toBe(1)
    })

    it('should return consistent results for same client', () => {
      const result1 = checkRateLimit(mockRequest)
      const result2 = checkRateLimit(mockRequest)

      expect(result1.allowed).toBe(true)
      expect(result2.allowed).toBe(true)
      expect(result2.totalHits).toBe(result1.totalHits + 1)
      expect(result2.remaining).toBe(result1.remaining - 1)
    })

  })

  describe('createRateLimitHeaders', () => {
    it('should create proper HTTP headers for rate limiting', () => {
      const result = checkRateLimit(mockRequest)

      const headers = createRateLimitHeaders(result)

      expect(headers).toEqual({
        'X-RateLimit-Limit': String(result.totalHits),
        'X-RateLimit-Remaining': String(result.remaining),
        'X-RateLimit-Reset': expect.any(String),
        'Retry-After': expect.any(String),
      })
    })
  })

  describe('logRateLimitViolation', () => {
    it('should log rate limit violations with proper context', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      const result = checkRateLimit(mockRequest)

      logRateLimitViolation(mockRequest, result)

      expect(consoleSpy).toHaveBeenCalledWith('Rate limit violation:', {
        ip: '***********',
        path: '/',
        remaining: result.remaining,
        resetTime: expect.any(String),
      })

      consoleSpy.mockRestore()
    })
  })

  describe('Rate Limiting Integration', () => {
    it('should apply different limits for different endpoints', () => {
      // This test verifies the configuration is set up correctly
      expect(authRateLimiter).toBeDefined()
      expect(authRateLimiter.checkLimit).toBeDefined()
    })

    it('should use auth rate limiter for authentication requests', () => {
      const result = authRateLimiter.checkLimit(mockRequest)
      expect(result.allowed).toBe(true)
      expect(result.remaining).toBeGreaterThanOrEqual(0)
      expect(result.resetTime).toBeGreaterThan(Date.now())
    })

    it('should enforce rate limits after multiple requests', () => {
      // Make multiple requests to test rate limiting
      const results = []
      for (let i = 0; i < 5; i++) {
        results.push(checkRateLimit(mockRequest))
      }

      // All requests should be tracked
      results.forEach((result, index) => {
        expect(result.totalHits).toBe(index + 1)
      })
    })
  })

  describe('Performance Impact', () => {
    it('should complete rate limit checks quickly', () => {
      const start = Date.now()
      checkRateLimit(mockRequest)
      const duration = Date.now() - start

      expect(duration).toBeLessThan(10) // Should complete in less than 10ms
    })
  })
})