import {
  generateCSRFToken,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getCSRFTokenFromRequest,
  validateCSRFFromRequest,
  validateCSRFMiddleware,
  CSRFConfig
} from '@/lib/csrf'
import { NextRequest } from 'next/server'

describe('CSRF Protection', () => {
  describe('generateCSRFToken', () => {
    it('should generate a valid base64 token', async () => {
      const token = await generateCSRFToken()
      expect(token).toMatch(/^[A-Za-z0-9+/]+=*$/) // Base64 format
      expect(token.length).toBeGreaterThan(32)
    })

    it('should generate different tokens each time', async () => {
      const token1 = await generateCSRFToken()
      const token2 = await generateCSRFToken()
      expect(token1).not.toBe(token2)
    })

    it('should generate HMAC-based token with timestamp', async () => {
      const token = await generateCSRFToken()
      expect(token).toBeDefined()

      // Token should be base64 encoded and contain data + signature
      const decoded = atob(token)
      expect(decoded).toContain('.')
    })
  })

  describe('validateCSRFToken', () => {
    it('should validate tokens correctly', async () => {
      const token = await generateCSRFToken()
      const isValid = await validateCSRFToken(token)
      expect(isValid).toBe(true)
    })

    it('should reject invalid tokens', async () => {
      const invalidToken = 'invalid-token'
      const isValid = await validateCSRFToken(invalidToken)
      expect(isValid).toBe(false)
    })

    it('should reject null or empty tokens', async () => {
      const isValidNull = await validateCSRFToken('')
      const isValidEmpty = await validateCSRFToken('')
      expect(isValidNull).toBe(false)
      expect(isValidEmpty).toBe(false)
    })

    it('should validate tokens with custom config', async () => {
      const customConfig: CSRFConfig = {
        secret: 'test-secret',
        tokenLength: 32,
        headerName: 'x-csrf-token',
        cookieName: 'csrf-token'
      }
      const token = await generateCSRFToken(customConfig)
      const isValid = await validateCSRFToken(token, customConfig)
      expect(isValid).toBe(true)
    })

    it('should reject expired tokens', async () => {
      const token = await generateCSRFToken()
      // Test with very short maxAge (1ms)
      const isValid = await validateCSRFToken(token, undefined, 1)
      // Wait a bit to ensure expiration
      await new Promise(resolve => setTimeout(resolve, 10))
      const isValidAfterExpiry = await validateCSRFToken(token, undefined, 1)
      expect(isValidAfterExpiry).toBe(false)
    })
  })

  describe('getCSRFTokenFromRequest', () => {
    it('should extract token from X-CSRF-Token header', () => {
      const request = new NextRequest('https://example.com', {
        headers: {
          'x-csrf-token': 'test-token'
        }
      })

      const token = getCSRFTokenFromRequest(request)
      expect(token).toBe('test-token')
    })

    it('should extract token from cookie when header is not present', () => {
      const request = new NextRequest('https://example.com', {
        headers: {
          'cookie': 'csrf-token=cookie-token'
        }
      })

      const token = getCSRFTokenFromRequest(request)
      expect(token).toBe('cookie-token')
    })

    it('should return null when no token is found', () => {
      const request = new NextRequest('https://example.com')

      const token = getCSRFTokenFromRequest(request)
      expect(token).toBeNull()
    })
  })

  describe('validateCSRFFromRequest', () => {
    it('should validate request with valid token in header', async () => {
      const token = await generateCSRFToken()
      const request = new NextRequest('https://example.com', {
        headers: {
          'x-csrf-token': token
        }
      })

      const isValid = await validateCSRFFromRequest(request)
      expect(isValid).toBe(true)
    })

    it('should validate request with valid token in cookie', async () => {
      const token = await generateCSRFToken()
      const request = new NextRequest('https://example.com', {
        headers: {
          'cookie': `csrf-token=${token}`
        }
      })

      const isValid = await validateCSRFFromRequest(request)
      expect(isValid).toBe(true)
    })

    it('should reject request with no token', async () => {
      const request = new NextRequest('https://example.com')

      const isValid = await validateCSRFFromRequest(request)
      expect(isValid).toBe(false)
    })

    it('should reject request with invalid token', async () => {
      const request = new NextRequest('https://example.com', {
        headers: {
          'x-csrf-token': 'invalid-token'
        }
      })

      const isValid = await validateCSRFFromRequest(request)
      expect(isValid).toBe(false)
    })

  })

  describe('validateCSRFMiddleware', () => {
    it('should validate CSRF tokens for requests', async () => {
      const token = await generateCSRFToken()
      const request = new NextRequest('https://example.com', {
        method: 'POST',
        headers: {
          'x-csrf-token': token
        }
      })

      const result = await validateCSRFMiddleware(request)
      expect(result).toBe(true)
    })

    it('should reject requests with invalid tokens', async () => {
      const request = new NextRequest('https://example.com', {
        method: 'POST',
        headers: {
          'x-csrf-token': 'invalid-token'
        }
      })

      const result = await validateCSRFMiddleware(request)
      expect(result).toBe(false)
    })

    it('should reject requests with no tokens', async () => {
      const request = new NextRequest('https://example.com', {
        method: 'POST'
      })

      const result = await validateCSRFMiddleware(request)
      expect(result).toBe(false)
    })
  })

  describe('Security Edge Cases', () => {
    it('should handle extremely long tokens gracefully', async () => {
      const longToken = 'a'.repeat(10000)

      const isValid = await validateCSRFToken(longToken)
      expect(isValid).toBe(false)
    })

    it('should handle malformed base64 tokens', async () => {
      const malformedToken = 'not-valid-base64!'

      const isValid = await validateCSRFToken(malformedToken)
      expect(isValid).toBe(false)
    })

    it('should handle tokens without proper structure', async () => {
      const invalidToken = btoa('no-dot-separator')

      const isValid = await validateCSRFToken(invalidToken)
      expect(isValid).toBe(false)
    })
  })

  describe('Token Expiration', () => {
    it('should generate tokens that can be validated immediately', async () => {
      const token = await generateCSRFToken()
      const isValid = await validateCSRFToken(token)
      expect(isValid).toBe(true)
    })

    it('should respect custom maxAge parameter', async () => {
      const token = await generateCSRFToken()
      // Test with very short maxAge
      const isValid = await validateCSRFToken(token, undefined, 1)
      // Wait to ensure expiration
      await new Promise(resolve => setTimeout(resolve, 10))
      const isValidAfterExpiry = await validateCSRFToken(token, undefined, 1)
      expect(isValidAfterExpiry).toBe(false)
    })
  })
})