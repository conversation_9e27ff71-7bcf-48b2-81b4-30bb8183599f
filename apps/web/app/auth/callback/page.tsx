'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClientSupabaseClient } from '@/lib/auth'
import { listUserTenants } from '@ledgerly/dal'

export default function AuthCallbackPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const supabase = createClientSupabaseClient()
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Auth callback error:', error)
          router.replace('/login?error=auth_callback_failed')
          return
        }

        if (data.session) {
          // Successfully authenticated - check if user needs onboarding
          try {
            const tenants = await listUserTenants()
            
            if (tenants.length === 0) {
              // No tenants - needs onboarding
              router.replace('/onboarding')
            } else {
              // Has tenants - redirect normally
              const redirectTo = searchParams.get('redirectTo') || '/'
              router.replace(redirectTo)
            }
          } catch (tenantError) {
            console.error('Error checking tenants:', tenantError)
            // If tenant check fails, still try onboarding
            router.replace('/onboarding')
          }
        } else {
          // No session found
          router.replace('/login?error=no_session')
        }
      } catch (err) {
        console.error('Auth callback error:', err)
        router.replace('/login?error=callback_error')
      }
    }

    handleAuthCallback()
  }, [router, searchParams])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Completing sign in...</p>
      </div>
    </div>
  )
}