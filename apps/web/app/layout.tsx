import type { Metadata } from 'next'
import './globals.css'
import { AuthProvider } from '@/contexts/AuthContext'
import { OrgEntityProvider } from '@/contexts/OrgEntityContext'
import { Navigation } from '@/components/Navigation'
import { generateCSRFToken } from '@/lib/csrf'
import { cookies, headers } from 'next/headers'
import { CSPProvider } from '@/contexts/CSPContext'

export const metadata: Metadata = {
  title: 'Ledgerly - Accounting Management',
  description: 'Modern accounting and ledger management application',
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Generate CSRF token for this request
  const csrfToken = await generateCSRFToken()

  // Set CSRF token as HttpOnly cookie for double-submit pattern
  const cookieStore = cookies()
  cookieStore.set('csrf-token', csrfToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: 60 * 60 * 24, // 24 hours
  })

  // Get CSP nonce from headers (set by middleware in production)  
  const headerStore = headers()
  const cspNonce = headerStore.get('X-CSP-Nonce')
  
  return (
    <html lang="en">
      <head>
        <meta name="csrf-token" content={csrfToken} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className="min-h-screen bg-background text-foreground">
        <CSPProvider nonce={cspNonce}>
          <AuthProvider>
            <OrgEntityProvider>
              <div className="flex min-h-screen">
                <Navigation />
                <main className="flex-1 p-6">
                  {children}
                </main>
              </div>
            </OrgEntityProvider>
          </AuthProvider>
        </CSPProvider>
      </body>
    </html>
  )
}