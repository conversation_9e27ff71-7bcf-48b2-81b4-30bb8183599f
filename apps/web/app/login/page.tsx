'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { signInWithEmail } from '@ledgerly/dal'
import { useAuth } from '@/contexts/AuthContext'
import { useCSRF } from '@/hooks/useCSRF'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user } = useAuth()
  const { createCSRFInput } = useCSRF()
  
  // Redirect if already logged in
  if (user) {
    const redirectTo = searchParams.get('redirectTo') || '/'
    router.replace(redirectTo)
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setMessage('')

    try {
      const redirectTo = searchParams.get('redirectTo') || '/'
      const redirectUrl = new URL('/auth/callback', window.location.origin)
      redirectUrl.searchParams.set('redirectTo', redirectTo)
      
      await signInWithEmail(email, redirectUrl.toString())
      setMessage('Check your email for the login link!')
    } catch (err) {
      // Sanitize error messages for security - never expose internal details
      const message = err instanceof Error ? err.message : 'An error occurred'
      
      // Only show safe, user-friendly messages
      if (message.includes('Invalid email') || message.includes('Email not found')) {
        setError('Please check your email address and try again.')
      } else if (message.includes('rate limit') || message.includes('Too many')) {
        setError('Too many attempts. Please wait a moment before trying again.')
      } else {
        // Generic error for anything else to prevent information disclosure
        setError('Unable to send login link. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
            Sign in to Ledgerly
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enter your email and we&apos;ll send you a secure login link
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {createCSRFInput()}
          <div>
            <label htmlFor="email" className="sr-only">
              Email address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="relative block w-full appearance-none rounded border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              placeholder="Enter your email address"
            />
          </div>

          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          {message && (
            <div className="rounded-md bg-green-50 p-4">
              <div className="text-sm text-green-700">{message}</div>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative flex w-full justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Sending...' : 'Send login link'}
            </button>
          </div>

          <div className="text-center space-y-2">
            <a
              href="/forgot-password"
              className="block text-sm text-blue-600 hover:text-blue-500"
            >
              Forgot your password?
            </a>
            <p className="text-sm text-gray-600">
              Don&apos;t have an account?{' '}
              <a href="/signup" className="text-blue-600 hover:text-blue-500 font-medium">
                Sign up
              </a>
            </p>
          </div>
        </form>
      </div>
    </div>
  )
}