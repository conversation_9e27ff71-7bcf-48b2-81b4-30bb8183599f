import { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@ledgerly/types'
import { createJsonResponse } from '@/lib/http/createJsonResponse'

/**
 * Update user profile
 * This route requires CSRF protection as it performs state-changing operations
 */
export async function PUT(request: NextRequest) {
  try {
    // Profile updates temporarily disabled - schema doesn't include profiles table
    return createJsonResponse({ error: 'Profile updates not available' }, 501)
  } catch (error) {
    console.error('API error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
}

/**
 * Get user profile
 * This is a safe method and doesn't require CSRF protection
 */
export async function GET(request: NextRequest) {
  try {
    // Profile fetching temporarily disabled - schema doesn't include profiles table
    return createJsonResponse({ error: 'Profile fetching not available' }, 501)
  } catch (error) {
    console.error('API error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
}