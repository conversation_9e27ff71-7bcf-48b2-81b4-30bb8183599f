import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@ledgerly/types'

/**
 * Create a new tenant
 * This route requires CSRF protection as it performs state-changing operations
 */
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { name, description } = await request.json()
    
    if (!name || name.trim() === '') {
      return NextResponse.json({ error: 'Tenant name is required' }, { status: 400 })
    }

    // Create tenant
    const { data: tenant, error } = await supabase
      .from('tenants')
      .insert([{
        name: name.trim(),
        description: description?.trim() || null,
        created_by: user.id,
      }])
      .select()
      .single()

    if (error) {
      console.error('Error creating tenant:', error)
      return NextResponse.json({ error: 'Failed to create tenant' }, { status: 500 })
    }

    // Add user as admin of the new tenant
    const { error: membershipError } = await supabase
      .from('tenant_memberships')
      .insert([{
        tenant_id: tenant.id,
        user_id: user.id,
        role: 'admin',
      }])

    if (membershipError) {
      console.error('Error creating tenant membership:', membershipError)
      // Don't fail the request, but log the error
    }

    return NextResponse.json({ tenant })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * Get user's tenants
 * This is a safe method and doesn't require CSRF protection
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: tenants, error } = await supabase
      .from('v_user_tenants')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching tenants:', error)
      return NextResponse.json({ error: 'Failed to fetch tenants' }, { status: 500 })
    }

    return NextResponse.json({ tenants })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}