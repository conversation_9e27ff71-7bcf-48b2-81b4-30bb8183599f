/**
 * Session Info API Route
 * Provides session timeout and security information
 */

import { NextRequest, NextResponse } from 'next/server'
import { createSecureServerClient, sessionManager } from '@/lib/session-security'

export async function GET(request: NextRequest) {
  try {
    const supabase = createSecureServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    const { data: { session } } = await supabase.auth.getSession()

    if (!user || !session) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    // Get session metadata
    const sessionMetadata = sessionManager.getSession(session.access_token)
    const timeoutInfo = sessionManager.getTimeoutInfo(session.access_token)
    const userSessions = sessionManager.getUserSessions(user.id)

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
      },
      session: {
        id: session.access_token.substring(0, 8) + '...',
        createdAt: sessionMetadata?.createdAt,
        lastActivity: sessionMetadata?.lastActivity,
        expiresAt: sessionMetadata?.expiresAt,
        deviceFingerprint: sessionMetadata?.deviceFingerprint?.substring(0, 8) + '...',
      },
      timeoutInfo,
      security: {
        activeSessions: userSessions.length,
        sessionStats: sessionManager.getSessionStats(),
      }
    })

  } catch (error) {
    console.error('Session info error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}