/**
 * Keep-Alive API Route
 * Extends session activity to prevent timeout
 */

import { NextRequest, NextResponse } from 'next/server'
import { createSecureServerClient, sessionManager } from '@/lib/session-security'
import { SecurityEvents, logSecurityEvent } from '@/lib/security-monitoring'

export async function POST(request: NextRequest) {
  try {
    const supabase = createSecureServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    const { data: { session } } = await supabase.auth.getSession()

    if (!user || !session) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    // Update session activity
    const activityUpdated = sessionManager.updateActivity(session.access_token, request)
    
    if (!activityUpdated) {
      // Session security violation
      logSecurityEvent({
        event_type: SecurityEvents.SUSPICIOUS_ACTIVITY,
        user_id: user.id,
        session_id: session.access_token,
        severity: 'high',
        details: {
          reason: 'Keep-alive failed - session security violation'
        }
      })

      return NextResponse.json(
        { error: 'Session security violation' },
        { status: 403 }
      )
    }

    // Get updated timeout info
    const timeoutInfo = sessionManager.getTimeoutInfo(session.access_token)

    return NextResponse.json({
      success: true,
      timeoutInfo,
      message: 'Session activity updated'
    })

  } catch (error) {
    console.error('Keep-alive error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}