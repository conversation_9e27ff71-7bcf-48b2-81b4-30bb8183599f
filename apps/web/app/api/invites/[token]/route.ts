import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@ledgerly/types'
import { getPendingInvite, cancelInvite } from '@ledgerly/dal'
import { validateCSRFMiddleware } from '@/lib/csrf'
import { logSecurityEvent, SecurityEvents } from '@/lib/security-monitoring'
import { getClientIP } from '@/lib/rate-limit'

// GET /api/invites/[token] - View invitation details
export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const { token } = params

    if (!token) {
      return NextResponse.json(
        { error: 'Missing invitation token' },
        { status: 400 }
      )
    }

    // Get invitation details (this doesn't require authentication)
    try {
      const invite = await getPendingInvite(token)
      
      if (!invite) {
        return NextResponse.json(
          { error: 'Invalid or expired invitation' },
          { status: 404 }
        )
      }

      // Return safe invitation details (without sensitive info)
      return NextResponse.json({
        scope: invite.scope,
        role: invite.role,
        inviterEmail: invite.inviter_user_id, // This would need to be resolved to email in a real app
        expiresAt: invite.expires_at,
        isExpired: new Date(invite.expires_at) < new Date(),
      })
      
    } catch (error) {
      console.error('Error fetching invitation:', error)
      return NextResponse.json(
        { error: 'Failed to fetch invitation details' },
        { status: 400 }
      )
    }
    
  } catch (error) {
    console.error('Get invitation API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/invites/[token] - Cancel invitation
export async function DELETE(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    // CSRF validation
    const csrfValidation = await validateCSRFMiddleware(request)
    if (!csrfValidation) {
      logSecurityEvent({
        type: SecurityEvents.SUSPICIOUS_ACTIVITY,
        severity: 'medium',
        details: {
          endpoint: `/api/invites/${params.token}`,
          reason: 'CSRF validation failed',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })
      
      return NextResponse.json(
        { error: 'Invalid CSRF token' }, 
        { status: 403 }
      )
    }

    // Get authenticated user
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      )
    }

    const { token } = params

    if (!token) {
      return NextResponse.json(
        { error: 'Missing invitation token' },
        { status: 400 }
      )
    }

    // Cancel invitation using DAL
    try {
      await cancelInvite(token)
      
      // Log successful invitation cancellation
      logSecurityEvent({
        type: SecurityEvents.ADMIN_ACTION,
        userId: user.id,
        severity: 'low',
        details: {
          action: 'cancel_invitation',
          token,
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })

      return NextResponse.json({ 
        success: true,
        message: 'Invitation cancelled successfully'
      })
      
    } catch (error) {
      console.error('Error cancelling invitation:', error)
      
      // Log failed invitation cancellation
      logSecurityEvent({
        type: SecurityEvents.PERMISSION_DENIED,
        userId: user.id,
        severity: 'medium',
        details: {
          action: 'cancel_invitation',
          token,
          error: error instanceof Error ? error.message : 'Unknown error',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })
      
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Failed to cancel invitation' },
        { status: 400 }
      )
    }
    
  } catch (error) {
    console.error('Cancel invitation API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}