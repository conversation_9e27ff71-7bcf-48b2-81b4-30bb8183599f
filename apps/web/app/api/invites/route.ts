import { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@ledgerly/types'
import { createInvite, listSentInvites } from '@ledgerly/dal'
import { validateCSRFMiddleware } from '@/lib/csrf'
import { logSecurityEvent, SecurityEvents } from '@/lib/security-monitoring'
import { getClientIP } from '@/lib/rate-limit'
import { createJsonResponse } from '@/lib/http/createJsonResponse'

export async function POST(request: NextRequest) {
  try {
    // CSRF validation
    const csrfValidation = await validateCSRFMiddleware(request)
    if (!csrfValidation) {
      logSecurityEvent({
        type: SecurityEvents.SUSPICIOUS_ACTIVITY,
        severity: 'medium',
        details: {
          endpoint: '/api/invites',
          reason: 'CSRF validation failed',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })
      
      return createJsonResponse(
        { error: 'Invalid CSRF token' },
        403
      )
    }

    // Get authenticated user
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return createJsonResponse(
        { error: 'Unauthorized' },
        401
      )
    }

    // Parse request body
    const body = await request.json()
    const { scope, scopeId, email, role } = body

    // Validate required fields
    if (!scope || !scopeId || !email || !role) {
      return createJsonResponse(
        { error: 'Missing required fields: scope, scopeId, email, role' },
        400
      )
    }

    // Validate scope
    if (!['tenant', 'entity'].includes(scope)) {
      return createJsonResponse(
        { error: 'Invalid scope. Must be "tenant" or "entity"' },
        400
      )
    }

    // Validate role based on scope
    const validTenantRoles = ['tenant_owner', 'tenant_admin', 'tenant_billing', 'tenant_member']
    const validEntityRoles = ['owner', 'admin', 'accountant', 'bookkeeper', 'viewer']
    
    if (scope === 'tenant' && !validTenantRoles.includes(role)) {
      return createJsonResponse(
        { error: `Invalid tenant role. Must be one of: ${validTenantRoles.join(', ')}` },
        400
      )
    }
    
    if (scope === 'entity' && !validEntityRoles.includes(role)) {
      return createJsonResponse(
        { error: `Invalid entity role. Must be one of: ${validEntityRoles.join(', ')}` },
        400
      )
    }

    // Create invitation using DAL
    try {
      const inviteToken = await createInvite(scope, scopeId.toString(), email, role)
      
      // Log invitation creation
      logSecurityEvent({
        type: SecurityEvents.ADMIN_ACTION,
        userId: user.id,
        severity: 'low',
        details: {
          action: 'create_invitation',
          scope,
          scopeId: scopeId.toString(),
          targetEmail: email,
          role,
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })

      return createJsonResponse({
        success: true,
        inviteToken,
        message: inviteToken ? 'Invitation sent' : 'User already exists and was granted access directly'
      })
      
    } catch (error) {
      console.error('Error creating invitation:', error)
      
      // Log failed invitation attempt
      logSecurityEvent({
        type: SecurityEvents.PERMISSION_DENIED,
        userId: user.id,
        severity: 'medium',
        details: {
          action: 'create_invitation',
          scope,
          scopeId: scopeId.toString(),
          targetEmail: email,
          role,
          error: error instanceof Error ? error.message : 'Unknown error',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })
      
      return createJsonResponse(
        { error: error instanceof Error ? error.message : 'Failed to create invitation' },
        400
      )
    }
    
  } catch (error) {
    console.error('Invitation API error:', error)
    return createJsonResponse(
      { error: 'Internal server error' },
      500
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return createJsonResponse(
        { error: 'Unauthorized' },
        401
      )
    }

    // Get sent invitations
    const invites = await listSentInvites()
    
    return createJsonResponse({ invites })
    
  } catch (error) {
    console.error('Error fetching invitations:', error)
    return createJsonResponse(
      { error: 'Internal server error' },
      500
    )
  }
}