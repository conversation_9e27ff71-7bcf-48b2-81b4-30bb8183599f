import { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@ledgerly/types'
import { acceptInvite } from '@ledgerly/dal'
import { validateCSRFMiddleware } from '@/lib/csrf'
import { logSecurityEvent, SecurityEvents } from '@/lib/security-monitoring'
import { getClientIP } from '@/lib/rate-limit'
import { createJsonResponse } from '@/lib/http/createJsonResponse'

export async function POST(request: NextRequest) {
  try {
    // CSRF validation
    const csrfValidation = await validateCSRFMiddleware(request)
    if (!csrfValidation) {
      logSecurityEvent({
        type: SecurityEvents.SUSPICIOUS_ACTIVITY,
        severity: 'medium',
        details: {
          endpoint: '/api/invites/accept',
          reason: 'CSRF validation failed',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })
      
      return createJsonResponse(
        { error: 'Invalid CSRF token' },
        403
      )
    }

    // Get authenticated user
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return createJsonResponse(
        { error: 'Unauthorized' },
        401
      )
    }

    // Parse request body
    const body = await request.json()
    const { token } = body

    // Validate required fields
    if (!token) {
      return createJsonResponse(
        { error: 'Missing required field: token' },
        400
      )
    }

    // Accept invitation using DAL
    try {
      await acceptInvite(token)
      
      // Log successful invitation acceptance
      logSecurityEvent({
        type: SecurityEvents.ADMIN_ACTION,
        userId: user.id,
        severity: 'low',
        details: {
          action: 'accept_invitation',
          token,
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })

      return createJsonResponse({
        success: true,
        message: 'Invitation accepted successfully'
      })
      
    } catch (error) {
      console.error('Error accepting invitation:', error)
      
      // Log failed invitation acceptance
      logSecurityEvent({
        type: SecurityEvents.PERMISSION_DENIED,
        userId: user.id,
        severity: 'medium',
        details: {
          action: 'accept_invitation',
          token,
          error: error instanceof Error ? error.message : 'Unknown error',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })
      
      return createJsonResponse(
        { error: error instanceof Error ? error.message : 'Failed to accept invitation' },
        400
      )
    }
    
  } catch (error) {
    console.error('Accept invitation API error:', error)
    return createJsonResponse(
      { error: 'Internal server error' },
      500
    )
  }
}