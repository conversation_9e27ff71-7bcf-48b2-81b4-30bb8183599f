'use client'

import { useEffect, useState } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import { SubscriptionCard } from '@/components/SubscriptionCard'
import { OrgEntityBreadcrumb } from '@/components/OrgEntityBreadcrumb'

interface ConnectionStatus {
  database: 'checking' | 'connected' | 'error'
  error?: string
}

export default function Dashboard() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    database: 'checking'
  })

  useEffect(() => {
    async function checkConnections() {
      // Test database connection
      try {
        const supabase = createClientSupabaseClient()
        const { error } = await supabase.from('tenants').select('count').limit(1)
        
        if (error) {
          setConnectionStatus({ 
            database: 'error', 
            error: error.message 
          })
        } else {
          setConnectionStatus({ database: 'connected' })
        }
      } catch (err) {
        setConnectionStatus({ 
          database: 'error', 
          error: err instanceof Error ? err.message : 'Connection failed'
        })
      }
    }

    void checkConnections()
  }, [])

  const getStatusColor = (status: 'checking' | 'connected' | 'error') => {
    switch (status) {
      case 'checking':
        return 'bg-yellow-100 text-yellow-800'
      case 'connected':
        return 'bg-green-100 text-green-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: 'checking' | 'connected' | 'error') => {
    switch (status) {
      case 'checking':
        return 'Checking...'
      case 'connected':
        return 'Connected'
      case 'error':
        return 'Error'
      default:
        return 'Unknown'
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <div className="mb-4">
          <OrgEntityBreadcrumb />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
        <p className="text-gray-600">Welcome to Ledgerly - Your accounting management hub</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {/* Environment Status Card */}
        <div className="bg-white rounded-lg shadow p-6 border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Environment Status</h2>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Database</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(connectionStatus.database)}`}>
                {getStatusText(connectionStatus.database)}
              </span>
            </div>
            
            {connectionStatus.error && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                {connectionStatus.error}
              </div>
            )}
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Next.js</span>
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Running
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">TypeScript</span>
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Active
              </span>
            </div>
          </div>
        </div>

        {/* Quick Actions Card */}
        <div className="bg-white rounded-lg shadow p-6 border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          
          <div className="space-y-3">
            <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded border border-gray-200">
              View Recent Transactions
            </button>
            <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded border border-gray-200">
              Generate VAT Report
            </button>
            <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded border border-gray-200">
              Export Trial Balance
            </button>
          </div>
        </div>

        {/* System Info Card */}
        <div className="bg-white rounded-lg shadow p-6 border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">System Info</h2>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-700">Version</span>
              <span className="text-sm text-gray-600">0.1.0</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-700">Environment</span>
              <span className="text-sm text-gray-600">
                {process.env.NODE_ENV || 'development'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-700">Framework</span>
              <span className="text-sm text-gray-600">Next.js 14</span>
            </div>
          </div>
        </div>

        {/* Subscription Card */}
        <SubscriptionCard tenantId={1} />
      </div>

      {/* Recent Activity Section */}
      <div className="mt-8 bg-white rounded-lg shadow p-6 border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
        
        <div className="text-center py-8 text-gray-500">
          <p>No recent activity to display</p>
          <p className="text-sm mt-1">Activity will appear here once you start using the application</p>
        </div>
      </div>
    </div>
  )
}