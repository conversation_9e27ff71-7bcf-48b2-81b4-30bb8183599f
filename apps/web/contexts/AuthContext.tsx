'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import type { User, Session } from '@supabase/supabase-js'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useSessionSecurity, useSessionTimeoutDialog, SessionSecurityHook } from '@/hooks/useSessionSecurity'
import { SessionTimeoutDialog } from '@/components/SessionTimeoutDialog'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signOut: () => Promise<void>
  sessionSecurity: SessionSecurityHook
}

const AuthContext = createContext<AuthContextType | null>(null)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [supabase] = useState(() => createSecureBrowserClient())

  // Session timeout dialog
  const timeoutDialog = useSessionTimeoutDialog()

  // Session security management
  const sessionSecurity = useSessionSecurity({
    onTimeoutWarning: timeoutDialog.onTimeoutWarning,
    onTimeout: (reason) => {
      console.log('Session timed out:', reason)
      // The useSessionSecurity hook handles the actual logout
    },
    enableActivityTracking: true,
    enableOnlineStatus: true,
    enableAutoRefresh: true,
  })

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)

        // Log security events
        if (event === 'SIGNED_IN' && session?.user) {
          // This will be logged by the session security manager
          console.log('User signed in, session security active')
        } else if (event === 'SIGNED_OUT') {
          console.log('User signed out')
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase])

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
    } catch (error) {
      console.error('Error signing out:', error)
      // Force sign out by clearing local state
      setUser(null)
      setSession(null)
    }
  }

  return (
    <AuthContext.Provider value={{
      user,
      session,
      loading,
      signOut,
      sessionSecurity
    }}>
      {children}
      
      {/* Session timeout warning dialog */}
      <SessionTimeoutDialog
        show={timeoutDialog.showDialog}
        timeoutInfo={timeoutDialog.timeoutInfo}
        onExtendSession={() => {
          sessionSecurity.extendSession()
          timeoutDialog.extendSession()
        }}
        onLogoutNow={() => {
          sessionSecurity.forceLogout()
          timeoutDialog.logoutNow()
        }}
        onClose={() => timeoutDialog.extendSession()}
      />
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}