'use client'

import { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { listUserTenants, listUserEntities } from '@ledgerly/dal'
import type { Database } from '@ledgerly/types'

type UserTenant = Database['public']['Views']['v_user_tenants']['Row']
type UserEntity = Database['public']['Views']['v_user_entities']['Row']

interface OrgEntitySelection {
  tenantId: number | null
  entityId: number | null
  tenantName?: string
  entityName?: string
  mode: 'tenant' | 'entity'
}

interface OrgEntityContextType {
  selection: OrgEntitySelection
  tenants: UserTenant[]
  entities: UserEntity[]
  loading: boolean
  error: string | null
  
  // Actions
  selectTenant: (tenant: UserTenant | null) => void
  selectEntity: (entity: UserEntity | null) => void
  refreshData: () => Promise<void>
}

const OrgEntityContext = createContext<OrgEntityContextType | undefined>(undefined)

const STORAGE_KEY = 'ledgerly_org_entity_selection'

function getStoredSelection(): OrgEntitySelection | null {
  if (typeof window === 'undefined') return null
  
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored ? JSON.parse(stored) : null
  } catch {
    return null
  }
}

function storeSelection(selection: OrgEntitySelection) {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(selection))
  } catch (error) {
    console.warn('Failed to store org/entity selection:', error)
  }
}

export function OrgEntityProvider({ children }: { children: React.ReactNode }) {
  const [selection, setSelection] = useState<OrgEntitySelection>(() => {
    return getStoredSelection() || {
      tenantId: null,
      entityId: null,
      mode: 'tenant'
    }
  })
  
  const [tenants, setTenants] = useState<UserTenant[]>([])
  const [entities, setEntities] = useState<UserEntity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const [tenantsData, entitiesData] = await Promise.all([
        listUserTenants(),
        listUserEntities()
      ])
      
      setTenants(tenantsData || [])
      setEntities(entitiesData || [])
      
      // Auto-select first tenant if none selected and user has tenants
      if (!selection.tenantId && tenantsData && tenantsData.length > 0) {
        const firstTenant = tenantsData[0]
        const newSelection = {
          tenantId: firstTenant.tenant_id,
          entityId: null,
          tenantName: firstTenant.tenant_name ?? undefined,
          mode: 'tenant' as const
        }
        setSelection(newSelection)
        storeSelection(newSelection)
      }
      
      // Validate stored selection still exists
      if (selection.tenantId) {
        const tenantExists = tenantsData?.some(t => t.tenant_id === selection.tenantId)
        if (!tenantExists) {
          const newSelection = {
            tenantId: null,
            entityId: null,
            mode: 'tenant' as const
          }
          setSelection(newSelection)
          storeSelection(newSelection)
        }
      }
      
      if (selection.entityId) {
        const entityExists = entitiesData?.some(e => e.entity_id === selection.entityId)
        if (!entityExists) {
          const newSelection = {
            ...selection,
            entityId: null,
            entityName: undefined,
            mode: 'tenant' as const
          }
          setSelection(newSelection)
          storeSelection(newSelection)
        }
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
      console.error('Error loading org/entity data:', err)
    } finally {
      setLoading(false)
    }
  }, [selection])

  const selectTenant = (tenant: UserTenant | null) => {
    const newSelection: OrgEntitySelection = {
      tenantId: tenant?.tenant_id || null,
      entityId: null, // Reset entity when switching tenants
      tenantName: tenant?.tenant_name ?? undefined,
      entityName: undefined,
      mode: 'tenant'
    }
    
    setSelection(newSelection)
    storeSelection(newSelection)
  }

  const selectEntity = (entity: UserEntity | null) => {
    const newSelection: OrgEntitySelection = {
      tenantId: entity?.tenant_id || selection.tenantId,
      entityId: entity?.entity_id || null,
      tenantName: entity?.tenant_name ?? selection.tenantName,
      entityName: entity?.entity_name ?? undefined,
      mode: 'entity'
    }
    
    setSelection(newSelection)
    storeSelection(newSelection)
  }

  useEffect(() => {
    loadData()
  }, [loadData])

  const contextValue: OrgEntityContextType = {
    selection,
    tenants,
    entities,
    loading,
    error,
    selectTenant,
    selectEntity,
    refreshData: loadData
  }

  return (
    <OrgEntityContext.Provider value={contextValue}>
      {children}
    </OrgEntityContext.Provider>
  )
}

export function useOrgEntity() {
  const context = useContext(OrgEntityContext)
  if (context === undefined) {
    throw new Error('useOrgEntity must be used within an OrgEntityProvider')
  }
  return context
}

// Convenience hooks
export function useCurrentTenant() {
  const { selection, tenants } = useOrgEntity()
  return tenants.find(t => t.tenant_id === selection.tenantId) || null
}

export function useCurrentEntity() {
  const { selection, entities } = useOrgEntity()
  return entities.find(e => e.entity_id === selection.entityId) || null
}

export function useEntitiesForTenant(tenantId: number | null) {
  const { entities } = useOrgEntity()
  return entities.filter(e => e.tenant_id === tenantId)
}