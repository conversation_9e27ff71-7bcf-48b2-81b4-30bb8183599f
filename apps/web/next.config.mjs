/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ['@ledgerly/types', '@ledgerly/dal'],
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },
  
  // Security Headers Configuration
  async headers() {
    return [
      {
        // Apply security headers to all routes
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'off'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), interest-cohort=()'
          },
          // HSTS - only enable in production with HTTPS
          process.env.NODE_ENV === 'production' && {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          },
          // Content Security Policy - Conditional based on environment
          {
            key: 'Content-Security-Policy',
            value: process.env.NODE_ENV === 'production' 
              ? [
                  "default-src 'self'",
                  "script-src 'self' 'nonce-NONCE_PLACEHOLDER'", // Production: nonce-based, no unsafe directives
                  "style-src 'self' 'nonce-NONCE_PLACEHOLDER'", // Production: nonce-based styles
                  "img-src 'self' data: https:",
                  "font-src 'self'",
                  "connect-src 'self' https://*.supabase.co https://*.supabase.in",
                  "frame-ancestors 'none'",
                  "base-uri 'self'",
                  "form-action 'self'",
                  "manifest-src 'self'",
                  "object-src 'none'", // Block plugins
                  "media-src 'self'",
                  "worker-src 'self' blob:", // Service workers
                ].join('; ')
              : [
                  "default-src 'self'",
                  "script-src 'self' 'unsafe-eval' 'unsafe-inline'", // Dev: allow unsafe for hot reload
                  "style-src 'self' 'unsafe-inline'", // Dev: allow inline styles
                  "img-src 'self' data: https:",
                  "font-src 'self'",
                  "connect-src 'self' https://*.supabase.co https://*.supabase.in ws: wss:", // Dev: websockets for HMR
                  "frame-ancestors 'none'",
                  "base-uri 'self'",
                  "form-action 'self'",
                  "manifest-src 'self'",
                ].join('; ')
          }
        ].filter(Boolean)
      },
      {
        // More restrictive CSP for production API routes
        source: '/api/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'none'; frame-ancestors 'none';"
          }
        ]
      }
    ]
  },

  // Experimental security features
  experimental: {
    // Enable strict CSP in production
    strictNextHead: true,
  }
}

export default nextConfig