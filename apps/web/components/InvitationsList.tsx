'use client'

import { useEffect, useState, useCallback } from 'react'
import { useCSRF } from '@/hooks/useCSRF'

interface Invitation {
  token: string
  scope: 'tenant' | 'entity'
  scope_id: string
  email: string
  role: string
  expires_at: string
  created_at: string
}

interface InvitationsListProps {
  refresh?: boolean
  onRefreshComplete?: () => void
}

export function InvitationsList({ refresh, onRefreshComplete }: InvitationsListProps) {
  const { addCSRFHeaders } = useCSRF()
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [cancelingTokens, setCancelingTokens] = useState<Set<string>>(new Set())

  const loadInvitations = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/invites')
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load invitations')
      }

      setInvitations(result.invites || [])
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load invitations')
    } finally {
      setIsLoading(false)
      onRefreshComplete?.()
    }
  }, [onRefreshComplete])

  const cancelInvitation = async (token: string) => {
    try {
      setCancelingTokens(prev => new Set(prev).add(token))

      const response = await fetch(`/api/invites/${token}`, {
        method: 'DELETE',
        headers: addCSRFHeaders()
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to cancel invitation')
      }

      // Remove the invitation from the list
      setInvitations(prev => prev.filter(inv => inv.token !== token))
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to cancel invitation')
    } finally {
      setCancelingTokens(prev => {
        const newSet = new Set(prev)
        newSet.delete(token)
        return newSet
      })
    }
  }

  const formatRole = (role: string) => {
    return role.replace('tenant_', '').replace('_', ' ').split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date()
  }

  useEffect(() => {
    loadInvitations()
  }, [loadInvitations])

  useEffect(() => {
    if (refresh) {
      loadInvitations()
    }
  }, [refresh, loadInvitations])

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          {[1, 2, 3].map(i => (
            <div key={i} className="flex items-center justify-between py-3 border-b border-gray-100">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-48"></div>
                <div className="h-3 bg-gray-200 rounded w-32"></div>
              </div>
              <div className="h-8 bg-gray-200 rounded w-16"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-8">
          <p className="text-red-600">{error}</p>
          <button
            onClick={loadInvitations}
            className="mt-2 px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700"
          >
            Try again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">Pending Invitations</h3>
          <button
            onClick={loadInvitations}
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            Refresh
          </button>
        </div>
      </div>

      <div className="divide-y divide-gray-100">
        {invitations.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            <p>No pending invitations</p>
            <p className="text-sm mt-1">Invitations you send will appear here</p>
          </div>
        ) : (
          invitations.map((invitation) => (
            <div key={invitation.token} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {invitation.email}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          invitation.scope === 'tenant' 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {invitation.scope === 'tenant' ? 'Organization' : 'Entity'}
                        </span>
                        <span className="text-sm text-gray-500">
                          {formatRole(invitation.role)}
                        </span>
                        {isExpired(invitation.expires_at) && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Expired
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">
                        Sent {formatDate(invitation.created_at)}
                      </p>
                      <p className="text-xs text-gray-400">
                        Expires {formatDate(invitation.expires_at)}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="ml-4 flex-shrink-0">
                  <button
                    onClick={() => cancelInvitation(invitation.token)}
                    disabled={cancelingTokens.has(invitation.token)}
                    className="text-sm text-red-600 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {cancelingTokens.has(invitation.token) ? 'Canceling...' : 'Cancel'}
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}