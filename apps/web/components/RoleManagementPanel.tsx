'use client'

import { useState, useEffect, useCallback } from 'react'
import { useCSRF } from '@/hooks/useCSRF'
import { useOrgEntity } from '@/contexts/OrgEntityContext'
import { listEntityMemberships } from '@ledgerly/dal'
import { InvitationForm } from './InvitationForm'
import { InvitationsList } from './InvitationsList'

interface Member {
  user_id: string
  role: string
  created_at: string
  user?: {
    email: string
    raw_user_meta_data?: {
      full_name?: string
    }
  }
}

export function RoleManagementPanel() {
  const { addCSRFHeaders } = useCSRF()
  const { selection, tenants, entities } = useOrgEntity()
  
  const [members, setMembers] = useState<Member[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showInviteForm, setShowInviteForm] = useState(false)
  const [refreshInvitations, setRefreshInvitations] = useState(false)
  const [changingRoles, setChangingRoles] = useState<Set<string>>(new Set())

  // Check if current user has admin permissions
  const currentTenant = tenants.find(t => t.tenant_id === selection.tenantId)
  const currentEntity = entities.find(e => e.entity_id === selection.entityId)
  
  const canManageTenant = currentTenant && currentTenant.role && ['tenant_owner', 'tenant_admin'].includes(currentTenant.role)
  const canManageEntity = currentEntity && currentEntity.role && ['owner', 'admin'].includes(currentEntity.role)

  const loadMembers = useCallback(async () => {
    if (!selection.tenantId) return

    try {
      setIsLoading(true)
      setError(null)

      // For now, just load entity members if entity is selected
      // In a full implementation, you'd load tenant members when tenant is selected
      if (selection.entityId && selection.mode === 'entity') {
        const entityMembers = await listEntityMemberships(selection.entityId)
        setMembers(entityMembers || [])
      } else {
        // TODO: Load tenant members
        setMembers([])
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load members')
    } finally {
      setIsLoading(false)
    }
  }, [selection.tenantId, selection.entityId, selection.mode])

  const changeRole = async (userId: string, newRole: string) => {
    try {
      setChangingRoles(prev => new Set(prev).add(userId))

      const response = await fetch('/api/roles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...addCSRFHeaders()
        },
        body: JSON.stringify({
          scope: selection.mode,
          scopeId: selection.mode === 'entity' ? selection.entityId : selection.tenantId,
          targetUserId: userId,
          role: newRole
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to change role')
      }

      // Update the member's role in the list
      setMembers(prev => prev.map(member => 
        member.user_id === userId 
          ? { ...member, role: newRole }
          : member
      ))
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to change role')
    } finally {
      setChangingRoles(prev => {
        const newSet = new Set(prev)
        newSet.delete(userId)
        return newSet
      })
    }
  }

  const formatRole = (role: string) => {
    return role.replace('tenant_', '').replace('_', ' ').split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  const getRoleOptions = () => {
    if (selection.mode === 'tenant') {
      return [
        { value: 'tenant_member', label: 'Member' },
        { value: 'tenant_billing', label: 'Billing' },
        { value: 'tenant_admin', label: 'Admin' },
        { value: 'tenant_owner', label: 'Owner' }
      ]
    } else {
      return [
        { value: 'viewer', label: 'Viewer' },
        { value: 'bookkeeper', label: 'Bookkeeper' },
        { value: 'accountant', label: 'Accountant' },
        { value: 'admin', label: 'Admin' },
        { value: 'owner', label: 'Owner' }
      ]
    }
  }

  const handleInviteSent = () => {
    setShowInviteForm(false)
    setRefreshInvitations(prev => !prev)
  }

  useEffect(() => {
    loadMembers()
  }, [loadMembers, selection.tenantId, selection.entityId, selection.mode])

  // Check if user has permission to manage roles
  const hasPermission = selection.mode === 'tenant' ? canManageTenant : canManageEntity

  if (!hasPermission) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-8">
          <p className="text-gray-500">You don&apos;t have permission to manage roles for this {selection.mode}.</p>
          <p className="text-sm text-gray-400 mt-1">Contact an admin or owner for access.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Role Management</h2>
            <p className="text-gray-600 mt-1">
              Manage access and permissions for {selection.mode === 'tenant' ? 'organization' : 'entity'} members
            </p>
          </div>
          <button
            onClick={() => setShowInviteForm(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Invite User
          </button>
        </div>
      </div>

      {/* Members List */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Current Members</h3>
        </div>

        <div className="divide-y divide-gray-100">
          {isLoading ? (
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-48"></div>
                        <div className="h-3 bg-gray-200 rounded w-24"></div>
                      </div>
                    </div>
                    <div className="h-8 bg-gray-200 rounded w-24"></div>
                  </div>
                ))}
              </div>
            </div>
          ) : error ? (
            <div className="p-6 text-center">
              <p className="text-red-600">{error}</p>
              <button
                onClick={loadMembers}
                className="mt-2 px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700"
              >
                Try again
              </button>
            </div>
          ) : members.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <p>No members found</p>
              <p className="text-sm mt-1">Invite users to get started</p>
            </div>
          ) : (
            members.map((member) => (
              <div key={member.user_id} className="p-6 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-gray-600">
                      {member.user?.email?.[0]?.toUpperCase() || '?'}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {member.user?.raw_user_meta_data?.full_name || member.user?.email || 'Unknown User'}
                    </p>
                    <p className="text-sm text-gray-500">{member.user?.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <select
                    value={member.role}
                    onChange={(e) => changeRole(member.user_id, e.target.value)}
                    disabled={changingRoles.has(member.user_id)}
                    className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {getRoleOptions().map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  
                  {changingRoles.has(member.user_id) && (
                    <div className="text-sm text-gray-500">Updating...</div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Pending Invitations */}
      <InvitationsList 
        refresh={refreshInvitations} 
        onRefreshComplete={() => setRefreshInvitations(false)}
      />

      {/* Invitation Form Modal */}
      {showInviteForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="max-w-md w-full mx-4">
            <InvitationForm 
              onInviteSent={handleInviteSent}
              onClose={() => setShowInviteForm(false)}
            />
          </div>
        </div>
      )}
    </div>
  )
}