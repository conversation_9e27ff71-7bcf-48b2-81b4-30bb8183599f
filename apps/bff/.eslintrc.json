{"root": false, "extends": ["../../.eslintrc.json"], "parserOptions": {"project": "./tsconfig.json"}, "env": {"es2022": true, "node": true}, "rules": {"no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "no-var": "error", "prefer-const": "warn", "no-trailing-spaces": "error", "eol-last": "error", "comma-dangle": ["error", "never"], "quotes": ["error", "single"], "semi": ["error", "never"], "@typescript-eslint/no-unsafe-assignment": "warn", "@typescript-eslint/no-unsafe-call": "warn", "@typescript-eslint/no-unsafe-member-access": "warn", "@typescript-eslint/no-unsafe-argument": "warn", "@typescript-eslint/no-unsafe-return": "warn", "@typescript-eslint/no-misused-promises": "warn", "@typescript-eslint/require-await": "warn", "@typescript-eslint/restrict-template-expressions": "warn"}, "ignorePatterns": ["dist/", "node_modules/"]}