import { FastifyPluginCallback, FastifyRequest } from 'fastify'
import fastifyPlugin from 'fastify-plugin'

export interface AuthPluginOptions {
  internalKey: string
}

declare module 'fastify' {
  interface FastifyRequest { // eslint-disable-line no-unused-vars
    isAuthenticated: boolean
    userToken?: string
    isUserRequest: boolean
  }
}

const authPlugin: FastifyPluginCallback<AuthPluginOptions> = async (fastify, options) => {
  fastify.decorateRequest('isAuthenticated', false)
  fastify.decorateRequest('userToken', null)
  fastify.decorateRequest('isUserRequest', false)

  fastify.addHook('preHandler', async (request: FastifyRequest, reply) => {
    // Skip auth for health check
    if (request.url === '/healthz') {
      request.isAuthenticated = true
      return
    }

    const internalKey = request.headers['x-internal-key']
    const authHeader = request.headers.authorization

    // Check for internal service-to-service authentication
    if (internalKey && internalKey === options.internalKey) {
      request.isAuthenticated = true

      // Check if this is a user request (has Authorization header)
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7)
        request.userToken = token
        request.isUserRequest = true

        // Create user-scoped Supabase client
        try {
          request.userSupabase = fastify.createUserClient(token)
        } catch (error) {
          fastify.log.error(error, 'Failed to create user Supabase client')
          void reply.status(401).send({
            success: false,
            error: 'Invalid user token'
          })
          return
        }
      }

      return
    }

    // No valid authentication
    request.isAuthenticated = false
    void reply.status(401).send({
      success: false,
      error: 'Unauthorized: Missing or invalid X-Internal-Key header'
    })
  })

  fastify.log.info('Auth plugin initialized with user context support')
}

export default fastifyPlugin(authPlugin, {
  name: 'auth-plugin'
})
