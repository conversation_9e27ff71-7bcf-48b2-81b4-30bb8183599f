"""Document extraction service with adapter pattern for different AI providers."""

import hashlib
import logging
from abc import ABC, abstractmethod
from typing import Any

from ..models import ExtractionResult, InvoiceInfo, LineItem, SupplierInfo, VatRate

logger = logging.getLogger(__name__)


class ExtractionAdapter(ABC):
    """Abstract base class for extraction adapters."""

    @abstractmethod
    async def extract(self, file_bytes: bytes, mime_type: str) -> ExtractionResult:
        """
        Extract structured data from document bytes.

        Args:
            file_bytes: Raw file content
            mime_type: MIME type of the file

        Returns:
            ExtractionResult with supplier, invoice, and line data

        Raises:
            ExtractionError: If extraction fails
        """
        pass


class ExtractionError(Exception):
    """Custom exception for extraction errors."""
    pass


class StubExtractionAdapter(ExtractionAdapter):
    """Deterministic stub adapter for testing and development."""

    async def extract(self, file_bytes: bytes, mime_type: str) -> ExtractionResult:
        """
        Generate deterministic extraction based on file hash.

        This ensures consistent results for testing while providing realistic data.
        """
        logger.info(f"Processing file with stub adapter (mime_type: {mime_type})")

        # Generate deterministic data based on file hash
        file_hash = hashlib.sha256(file_bytes).hexdigest()[:8]

        # Use hash to determine supplier and amounts
        hash_int = int(file_hash, 16)
        supplier_idx = hash_int % 3
        amount_base = (hash_int % 1000) + 100  # 100-1099 range

        suppliers = [
            {"name": "ACME Office Supplies", "vat": "BE0123456789"},
            {"name": "Green Energy Solutions", "vat": "BE0987654321"},
            {"name": "Premium Consulting Services", "vat": "BE0555123456"}
        ]

        supplier = suppliers[supplier_idx]
        net_amount = f"{amount_base}.00"
        vat_amount = f"{amount_base * 0.21:.2f}"
        gross_amount = f"{amount_base * 1.21:.2f}"

        return ExtractionResult(
            supplier=SupplierInfo(
                name=supplier["name"],
                vat=supplier["vat"],
                iban=f"BE{hash_int % 100:02d} 1234 5678 9012"
            ),
            invoice=InvoiceInfo(
                number=f"INV-2024-{hash_int % 1000:03d}",
                issue_date="2024-08-26",
                due_date="2024-09-26",
                currency="EUR",
                net=net_amount,
                vat=vat_amount,
                gross=gross_amount
            ),
            lines=[
                LineItem(
                    description=f"Professional services - {hash_int % 100}",
                    quantity="1",
                    unit_price=net_amount,
                    vat_rate=VatRate.STANDARD,
                    account_hint=6000
                )
            ],
            confidence=0.85 + (hash_int % 15) / 100  # 0.85-0.99 range
        )


class OpenAIExtractionAdapter(ExtractionAdapter):
    """OpenAI-powered extraction adapter."""

    def __init__(self, api_key: str, model: str = "gpt-4"):
        """Initialize OpenAI adapter."""
        self.api_key = api_key
        self.model = model
        # TODO: Initialize OpenAI client when implemented

    async def extract(self, file_bytes: bytes, mime_type: str) -> ExtractionResult:
        """
        Extract using OpenAI API.

        TODO: Implement actual OpenAI integration
        - OCR the document using pytesseract
        - Send extracted text to GPT-4 with structured prompt
        - Parse response into ExtractionResult
        """
        # For now, fallback to stub for development
        logger.warning("OpenAI adapter not yet implemented, using stub")
        stub_adapter = StubExtractionAdapter()
        return await stub_adapter.extract(file_bytes, mime_type)


class ExtractionService:
    """Main extraction service that uses configured adapter."""

    def __init__(self, adapter: ExtractionAdapter):
        """Initialize service with extraction adapter."""
        self.adapter = adapter

    async def process_document(
        self,
        file_bytes: bytes,
        mime_type: str,
        entity_id: int,
        supplier_templates: dict[str, Any] | None = None
    ) -> ExtractionResult:
        """
        Process document with extraction and supplier template hints.

        Args:
            file_bytes: Raw file content
            mime_type: MIME type
            entity_id: Entity ID for template lookup
            supplier_templates: Optional pre-loaded supplier templates

        Returns:
            ExtractionResult with potential account hints from templates
        """
        logger.info(f"Processing document for entity {entity_id}")

        try:
            # Perform extraction
            result = await self.adapter.extract(file_bytes, mime_type)

            # Enhance with supplier template hints if available
            if supplier_templates and result.supplier.vat:
                template = supplier_templates.get(result.supplier.vat)
                if template:
                    logger.info(f"Found supplier template for VAT {result.supplier.vat}")
                    # Update account hints for all lines
                    for line in result.lines:
                        if not line.account_hint and template.get("default_account_id"):
                            line.account_hint = template["default_account_id"]

            logger.info(f"Extraction completed with confidence {result.confidence}")
            return result

        except Exception as e:
            logger.error(f"Extraction failed: {str(e)}")
            raise ExtractionError(f"Failed to extract document: {str(e)}") from e


def create_extraction_service(provider: str = "stub", **kwargs) -> ExtractionService:
    """
    Factory function to create extraction service with specified provider.

    Args:
        provider: Provider name ('stub' or 'openai')
        **kwargs: Provider-specific configuration

    Returns:
        Configured ExtractionService
    """
    if provider == "stub":
        adapter = StubExtractionAdapter()
    elif provider == "openai":
        api_key = kwargs.get("api_key")
        if not api_key:
            raise ValueError("OpenAI API key required")
        adapter = OpenAIExtractionAdapter(api_key, kwargs.get("model", "gpt-4"))
    else:
        raise ValueError(f"Unknown provider: {provider}")

    return ExtractionService(adapter)
