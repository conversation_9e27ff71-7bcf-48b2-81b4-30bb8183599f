"""OCR processing service using pytesseract."""

import logging
from pathlib import Path
from typing import Any

import pdf2image
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter

from ..config import settings

logger = logging.getLogger(__name__)


class OCRService:
    """OCR service for extracting text from images and PDFs."""

    def __init__(self, language: str = None):
        """
        Initialize OCR service.

        Args:
            language: OCR language code (default from settings)
        """
        self.language = language or settings.OCR_LANGUAGE
        self.confidence_threshold = 60  # Minimum confidence for text extraction

        # Verify tesseract is available
        try:
            pytesseract.get_tesseract_version()
            logger.info(f"Tesseract version: {pytesseract.get_tesseract_version()}")
        except Exception as e:
            logger.error(f"Tesseract not available: {e}")
            raise RuntimeError("Tesseract OCR is not properly installed") from e

    def preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        Preprocess image to improve OCR accuracy.

        Args:
            image: PIL Image object

        Returns:
            Preprocessed PIL Image
        """
        try:
            # Convert to grayscale
            if image.mode != 'L':
                image = image.convert('L')

            # Enhance contrast
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)

            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(2.0)

            # Apply slight blur to reduce noise
            image = image.filter(ImageFilter.MedianFilter(size=3))

            # Resize if image is too small (improves OCR for small text)
            width, height = image.size
            if width < 300 or height < 300:
                scale_factor = max(300 / width, 300 / height)
                new_size = (int(width * scale_factor), int(height * scale_factor))
                image = image.resize(new_size, Image.Resampling.LANCZOS)

            return image

        except Exception as e:
            logger.warning(f"Image preprocessing failed: {e}")
            return image

    def extract_text_from_image(
        self,
        image_path: Path,
        preprocess: bool = True
    ) -> dict[str, Any]:
        """
        Extract text from an image file.

        Args:
            image_path: Path to image file
            preprocess: Whether to preprocess the image

        Returns:
            Dictionary containing extracted text and metadata
        """
        try:
            logger.info(f"Extracting text from image: {image_path}")

            # Load image
            with Image.open(image_path) as image:
                # Preprocess if requested
                if preprocess:
                    image = self.preprocess_image(image)

                # Extract text with confidence scores
                ocr_data = pytesseract.image_to_data(
                    image,
                    lang=self.language,
                    output_type=pytesseract.Output.DICT
                )

                # Extract plain text
                text = pytesseract.image_to_string(
                    image,
                    lang=self.language
                ).strip()

                # Calculate average confidence
                confidences = [int(conf) for conf in ocr_data['conf'] if int(conf) > 0]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                # Extract words with high confidence
                high_confidence_words = []
                for i, conf in enumerate(ocr_data['conf']):
                    if int(conf) > self.confidence_threshold:
                        word = ocr_data['text'][i].strip()
                        if word:
                            high_confidence_words.append({
                                'text': word,
                                'confidence': int(conf),
                                'bbox': (
                                    ocr_data['left'][i],
                                    ocr_data['top'][i],
                                    ocr_data['width'][i],
                                    ocr_data['height'][i]
                                )
                            })

                # Get image metadata
                image_info = {
                    'width': image.width,
                    'height': image.height,
                    'mode': image.mode,
                    'format': image.format
                }

                result = {
                    'text': text,
                    'metadata': {
                        'ocr_engine': 'tesseract',
                        'language': self.language,
                        'average_confidence': round(avg_confidence, 2),
                        'high_confidence_words_count': len(high_confidence_words),
                        'total_words_detected': len([w for w in ocr_data['text'] if w.strip()]),
                        'character_count': len(text),
                        'word_count': len(text.split()),
                        'image_info': image_info,
                        'preprocessing_applied': preprocess
                    },
                    'words': high_confidence_words
                }

                logger.info(f"OCR completed. Extracted {len(text)} characters with {avg_confidence:.1f}% confidence")
                return result

        except Exception as e:
            logger.error(f"OCR extraction failed for {image_path}: {e}")
            return {
                'text': '',
                'metadata': {
                    'ocr_engine': 'tesseract',
                    'error': str(e)
                },
                'words': []
            }

    def extract_text_from_pdf(
        self,
        pdf_path: Path,
        max_pages: int | None = None,
        dpi: int = 200
    ) -> dict[str, Any]:
        """
        Extract text from PDF using OCR.

        Args:
            pdf_path: Path to PDF file
            max_pages: Maximum number of pages to process
            dpi: DPI for PDF to image conversion

        Returns:
            Dictionary containing extracted text and metadata
        """
        try:
            logger.info(f"Extracting text from PDF using OCR: {pdf_path}")

            # Convert PDF pages to images
            try:
                pages = pdf2image.convert_from_path(
                    pdf_path,
                    dpi=dpi,
                    first_page=1,
                    last_page=max_pages,
                    thread_count=2
                )
            except Exception as e:
                logger.error(f"PDF to image conversion failed: {e}")
                return {
                    'text': '',
                    'metadata': {
                        'ocr_engine': 'tesseract',
                        'error': f'PDF conversion failed: {str(e)}'
                    },
                    'pages': []
                }

            all_text = []
            all_words = []
            page_results = []
            total_confidence = 0
            total_pages = len(pages)

            logger.info(f"Processing {total_pages} pages with OCR")

            for page_num, page_image in enumerate(pages, 1):
                try:
                    logger.debug(f"Processing page {page_num}/{total_pages}")

                    # Extract text from this page
                    page_result = self._extract_text_from_pil_image(page_image, page_num)

                    page_text = page_result['text']
                    page_confidence = page_result['metadata'].get('average_confidence', 0)

                    all_text.append(page_text)
                    all_words.extend(page_result['words'])
                    page_results.append(page_result)
                    total_confidence += page_confidence

                except Exception as e:
                    logger.error(f"OCR failed for page {page_num}: {e}")
                    page_results.append({
                        'text': '',
                        'metadata': {'error': str(e), 'page_number': page_num},
                        'words': []
                    })

            # Combine results
            combined_text = '\n\n--- Page Break ---\n\n'.join(all_text)
            avg_confidence = total_confidence / total_pages if total_pages > 0 else 0

            result = {
                'text': combined_text,
                'metadata': {
                    'ocr_engine': 'tesseract',
                    'language': self.language,
                    'pages_processed': total_pages,
                    'average_confidence': round(avg_confidence, 2),
                    'total_words_detected': len(all_words),
                    'character_count': len(combined_text),
                    'word_count': len(combined_text.split()),
                    'dpi': dpi,
                    'extraction_method': 'ocr'
                },
                'pages': page_results,
                'words': all_words
            }

            logger.info(f"PDF OCR completed. Extracted {len(combined_text)} characters from {total_pages} pages")
            return result

        except Exception as e:
            logger.error(f"PDF OCR extraction failed for {pdf_path}: {e}")
            return {
                'text': '',
                'metadata': {
                    'ocr_engine': 'tesseract',
                    'error': str(e)
                },
                'pages': []
            }

    def _extract_text_from_pil_image(
        self,
        image: Image.Image,
        page_number: int = None
    ) -> dict[str, Any]:
        """
        Extract text from PIL Image object.

        Args:
            image: PIL Image object
            page_number: Page number (for metadata)

        Returns:
            Dictionary containing extracted text and metadata
        """
        try:
            # Preprocess image
            processed_image = self.preprocess_image(image)

            # Extract text with confidence
            ocr_data = pytesseract.image_to_data(
                processed_image,
                lang=self.language,
                output_type=pytesseract.Output.DICT
            )

            # Extract plain text
            text = pytesseract.image_to_string(
                processed_image,
                lang=self.language
            ).strip()

            # Calculate confidence
            confidences = [int(conf) for conf in ocr_data['conf'] if int(conf) > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0

            # Extract high-confidence words
            words = []
            for i, conf in enumerate(ocr_data['conf']):
                if int(conf) > self.confidence_threshold:
                    word = ocr_data['text'][i].strip()
                    if word:
                        words.append({
                            'text': word,
                            'confidence': int(conf),
                            'bbox': (
                                ocr_data['left'][i],
                                ocr_data['top'][i],
                                ocr_data['width'][i],
                                ocr_data['height'][i]
                            )
                        })

            metadata = {
                'page_number': page_number,
                'average_confidence': round(avg_confidence, 2),
                'words_detected': len(words),
                'character_count': len(text),
                'word_count': len(text.split()),
                'image_size': (image.width, image.height)
            }

            return {
                'text': text,
                'metadata': metadata,
                'words': words
            }

        except Exception as e:
            logger.error(f"PIL image OCR failed: {e}")
            return {
                'text': '',
                'metadata': {
                    'page_number': page_number,
                    'error': str(e)
                },
                'words': []
            }

    def detect_language(self, image_path: Path) -> str:
        """
        Detect the language of text in an image.

        Args:
            image_path: Path to image file

        Returns:
            Language code
        """
        try:
            with Image.open(image_path) as image:
                # Use tesseract's orientation and script detection
                osd = pytesseract.image_to_osd(image)

                # Extract language from OSD output
                for line in osd.split('\n'):
                    if 'Script:' in line:
                        script = line.split(':')[1].strip()
                        # Map common scripts to language codes
                        script_to_lang = {
                            'Latin': 'eng',
                            'Han': 'chi_sim',
                            'Hiragana': 'jpn',
                            'Arabic': 'ara',
                            'Cyrillic': 'rus'
                        }
                        return script_to_lang.get(script, 'eng')

                return 'eng'  # Default to English

        except Exception as e:
            logger.warning(f"Language detection failed: {e}")
            return 'eng'  # Default to English

    def get_supported_languages(self) -> list[str]:
        """
        Get list of supported languages.

        Returns:
            List of language codes
        """
        try:
            langs = pytesseract.get_languages(config='')
            return langs
        except Exception as e:
            logger.error(f"Failed to get supported languages: {e}")
            return ['eng']  # Return default language
