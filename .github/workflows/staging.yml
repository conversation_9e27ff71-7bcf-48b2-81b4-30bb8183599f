name: Staging (develop)
on:
  push:
    branches: [develop]
  workflow_dispatch: {}
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: '20'
  PYTHON_VERSION: '3.11'

jobs:
  ci-full:
    name: Full Integration Checks
    runs-on: ubuntu-latest
    environment: staging
    timeout-minutes: 45
    
    services:
      postgres:
        image: ankane/pgvector:v0.5.1
        env:
          POSTGRES_DB: postgres
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Enable corepack
        run: corepack enable

      - name: Install pnpm
        run: corepack prepare pnpm@9 --activate

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Setup Python environment
        working-directory: apps/workers-py
        run: |
          uv venv
          uv sync

      - name: Install system dependencies for Python workers
        run: |
          sudo apt-get update
          sudo apt-get install -y tesseract-ocr poppler-utils

      - name: Lint and typecheck JavaScript/TypeScript
        run: |
          pnpm lint
          pnpm typecheck

      - name: Build JavaScript/TypeScript projects
        run: pnpm build

      - name: Setup database environment
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/postgres
        run: |
          # Apply migrations
          psql $DATABASE_URL -f packages/db/migrations/all.sql

      - name: Run SQL invariant tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/postgres
        run: |
          # Run database tests
          psql $DATABASE_URL -f packages/db/tests/001_invariants.sql

      - name: Run JavaScript/TypeScript tests (including integration)
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/postgres
          REDIS_URL: redis://localhost:6379
        run: |
          pnpm test
          # Run integration tests if they exist
          pnpm test:integration || echo "No integration tests found"

      - name: Test Python code
        working-directory: apps/workers-py
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/postgres
          REDIS_URL: redis://localhost:6379
        run: |
          source .venv/bin/activate
          uv run pytest

      - name: Security audit
        run: |
          pnpm audit --audit-level=moderate || true

  e2e-against-staging:
    name: E2E Tests (Staging URL)
    needs: ci-full
    runs-on: ubuntu-latest
    environment: staging
    timeout-minutes: 30
    if: vars.STAGING_BASE_URL != ''
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Enable corepack
        run: corepack enable

      - name: Install pnpm
        run: corepack prepare pnpm@9 --activate

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install Playwright (if E2E tests exist)
        run: |
          if pnpm run | grep -q "test:e2e"; then
            npx playwright install --with-deps
          else
            echo "No E2E suite found; skipping browser install"
          fi

      - name: Wait for staging deployment to be ready
        env:
          URL: ${{ vars.STAGING_BASE_URL }}
        run: |
          echo "Waiting for staging deployment at $URL to be ready..."
          for i in {1..60}; do
            if curl -sSf "$URL" >/dev/null 2>&1; then 
              echo "✅ Staging deployment is ready"
              exit 0
            fi
            echo "⏳ Attempt $i/60: Staging not ready yet, waiting 10s..."
            sleep 10
          done
          echo "❌ Staging deployment did not become ready in time"
          exit 1

      - name: Run E2E tests against staging
        env:
          BASE_URL: ${{ vars.STAGING_BASE_URL }}
        run: |
          if pnpm run | grep -q "test:e2e"; then
            echo "🧪 Running E2E tests against $BASE_URL"
            pnpm test:e2e -- --baseURL="$BASE_URL"
          else
            echo "ℹ️  No E2E test suite configured; skipping"
          fi
