name: Release (production)
on:
  pull_request:
    branches: [main]   # PRs from develop -> main
  push:
    branches: [main]   # After merge, deploy to prod
  workflow_dispatch: {}
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: '20'
  PYTHON_VERSION: '3.11'

jobs:
  ci-release:
    name: Release Validation
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Enable corepack
        run: corepack enable

      - name: Install pnpm
        run: corepack prepare pnpm@9 --activate

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Setup Python environment
        working-directory: apps/workers-py
        run: |
          uv venv
          uv sync

      - name: Lint and typecheck
        run: |
          pnpm lint
          pnpm typecheck

      - name: Run tests
        run: pnpm test

      - name: Build for production
        run: pnpm build

      - name: Verify staging deployment succeeded
        env:
          STAGING_URL: ${{ vars.STAGING_BASE_URL }}
        run: |
          if [ -n "$STAGING_URL" ]; then
            echo "🔍 Verifying staging deployment at $STAGING_URL"
            if curl -sSf "$STAGING_URL" >/dev/null 2>&1; then
              echo "✅ Staging deployment verified"
            else
              echo "❌ Staging deployment verification failed"
              exit 1
            fi
          else
            echo "⚠️  No staging URL configured; skipping verification"
          fi

  deploy-prod:
    name: Deploy to Production
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    environment:
      name: production
      url: ${{ steps.deploy.outputs.url }}
    timeout-minutes: 20
    permissions:
      contents: read
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Vercel CLI
        run: npm i -g vercel@latest

      - name: Pull Vercel environment information
        run: vercel pull --yes --environment=production --token ${{ secrets.VERCEL_TOKEN }}

      - name: Build project artifacts
        run: vercel build --prod --token ${{ secrets.VERCEL_TOKEN }}

      - name: Deploy to production
        id: deploy
        run: |
          URL=$(vercel deploy --prebuilt --prod --token ${{ secrets.VERCEL_TOKEN }})
          echo "url=$URL" >> $GITHUB_OUTPUT
          echo "🚀 Production deployment: $URL"

      - name: Verify production deployment
        env:
          PROD_URL: ${{ steps.deploy.outputs.url }}
        run: |
          echo "🔍 Verifying production deployment at $PROD_URL"
          for i in {1..30}; do
            if curl -sSf "$PROD_URL" >/dev/null 2>&1; then
              echo "✅ Production deployment verified"
              exit 0
            fi
            echo "⏳ Attempt $i/30: Production not ready yet, waiting 10s..."
            sleep 10
          done
          echo "❌ Production deployment verification failed"
          exit 1

      - name: Post-deployment health check
        env:
          PROD_URL: ${{ steps.deploy.outputs.url }}
        run: |
          echo "🏥 Running post-deployment health checks..."
          # Add your health check endpoints here
          # curl -sSf "$PROD_URL/api/health" || exit 1
          echo "✅ Health checks passed"
