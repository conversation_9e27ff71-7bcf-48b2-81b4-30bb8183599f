{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": ["./tsconfig.json", "./apps/*/tsconfig.json", "./packages/*/tsconfig.json"]}, "extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:security/recommended-legacy"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-var-requires": "warn", "@typescript-eslint/no-floating-promises": "warn", "@typescript-eslint/no-misused-promises": "warn", "@typescript-eslint/no-unsafe-assignment": "warn", "@typescript-eslint/no-unsafe-call": "warn", "@typescript-eslint/no-unsafe-member-access": "warn", "@typescript-eslint/no-unsafe-argument": "warn", "@typescript-eslint/no-unsafe-return": "warn", "@typescript-eslint/require-await": "warn", "@typescript-eslint/no-unnecessary-type-assertion": "warn", "security/detect-object-injection": "warn", "security/detect-non-literal-regexp": "warn", "security/detect-unsafe-regex": "warn", "security/detect-possible-timing-attacks": "warn", "react/no-unescaped-entities": "error", "react-hooks/exhaustive-deps": "error", "prefer-const": "warn"}, "ignorePatterns": ["dist/", "build/", "node_modules/", "*.js", "*.mjs", "**/supabase.ts"]}